const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Purchase = sequelize.define('purchase', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'project',
      key: 'id'
    }
  },
  supplierId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'supplier',
      key: 'id'
    }
  },
  purchaseNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  purchaseDate: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  status: {
    type: DataTypes.ENUM('draft', 'pending', 'approved', 'rejected', 'completed'),
    defaultValue: 'draft',
    allowNull: false
  },
  totalAmount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  currency: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'CNY'
  },
  paymentStatus: {
    type: DataTypes.ENUM('unpaid', 'partially_paid', 'paid'),
    defaultValue: 'unpaid',
    allowNull: false
  },
  paymentDueDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  items: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  expectedDeliveryDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  deliveryAddress: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  recipient: {
    type: DataTypes.STRING,
    allowNull: true
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'user',
      key: 'id'
    }
  }
}, {
  timestamps: true,
  paranoid: true
});

// Define associations
const setupAssociations = (models) => {
  const { Project, Supplier, User } = models;

  Purchase.belongsTo(Project, {
    foreignKey: 'projectId',
    as: 'project'
  });

  Purchase.belongsTo(Supplier, {
    foreignKey: 'supplierId',
    as: 'supplier'
  });

  Purchase.belongsTo(User, {
    foreignKey: 'createdBy',
    as: 'creator'
  });
};

module.exports = { Purchase, setupAssociations };