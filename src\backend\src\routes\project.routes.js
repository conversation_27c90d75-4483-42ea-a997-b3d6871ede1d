const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const projectValidation = require('../validations/project.validation');
const contractValidation = require('../validations/contract.validation');
const procurementValidation = require('../validations/procurement.validation');
const projectController = require('../controllers/project.controller');
const projectProgressController = require('../controllers/projectProgress.controller');
const projectTaskController = require('../controllers/projectTask.controller');
const projectFollowUpController = require('../controllers/projectFollowUp.controller');
const contractController = require('../controllers/contract.controller');
const procurementController = require('../controllers/procurement.controller');

const router = express.Router();

/**
 * @swagger
 * /projects/{projectId}/tasks:
 *   post:
 *     summary: Create a new project task
 *     description: Create a new task for a specific project.
 *     tags: [Project Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *         description: Project ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - taskType
 *               - assigneeId
 *               - plannedStartDate
 *               - plannedEndDate
 *             properties:
 *               name:
 *                 type: string
 *               taskType:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [not_started, in_progress, completed, on_hold, cancelled]
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, urgent]
 *               assigneeId:
 *                 type: string
 *               plannedStartDate:
 *                 type: string
 *                 format: date-time
 *               plannedEndDate:
 *                 type: string
 *                 format: date-time
 *               actualStartDate:
 *                 type: string
 *                 format: date-time
 *               actualEndDate:
 *                 type: string
 *                 format: date-time
 *               remainingWork:
 *                 type: number
 *               description:
 *                 type: string
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ProjectTask'
 *       "400":
 *         description: Bad request
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 *       "404":
 *         description: Project not found
 *   get:
 *     summary: Get all tasks for a project
 *     description: Retrieve all tasks for a specific project.
 *     tags: [Project Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *         description: Project ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [not_started, in_progress, completed, on_hold, cancelled]
 *         description: Filter by task status
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, medium, high, urgent]
 *         description: Filter by task priority
 *       - in: query
 *         name: assigneeId
 *         schema:
 *           type: string
 *         description: Filter by assignee ID
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Sort by field (e.g. name, priority, status)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Maximum number of tasks
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ProjectTask'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 totalPages:
 *                   type: integer
 *                   example: 1
 *                 totalResults:
 *                   type: integer
 *                   example: 1
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 *       "404":
 *         description: Project not found
 *
 * /projects/tasks/{taskId}:
 *   get:
 *     summary: Get a project task
 *     description: Get details of a specific project task.
 *     tags: [Project Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: taskId
 *         required: true
 *         schema:
 *           type: string
 *         description: Task ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ProjectTask'
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 *       "404":
 *         description: Task not found
 *   patch:
 *     summary: Update a project task
 *     description: Update details of a specific project task.
 *     tags: [Project Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: taskId
 *         required: true
 *         schema:
 *           type: string
 *         description: Task ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               taskType:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [not_started, in_progress, completed, on_hold, cancelled]
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, urgent]
 *               assigneeId:
 *                 type: string
 *               plannedStartDate:
 *                 type: string
 *                 format: date-time
 *               plannedEndDate:
 *                 type: string
 *                 format: date-time
 *               actualStartDate:
 *                 type: string
 *                 format: date-time
 *               actualEndDate:
 *                 type: string
 *                 format: date-time
 *               remainingWork:
 *                 type: number
 *               description:
 *                 type: string
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ProjectTask'
 *       "400":
 *         description: Bad request
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 *       "404":
 *         description: Task not found
 *   delete:
 *     summary: Delete a project task
 *     description: Delete a specific project task.
 *     tags: [Project Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: taskId
 *         required: true
 *         schema:
 *           type: string
 *         description: Task ID
 *     responses:
 *       "204":
 *         description: No content
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 *       "404":
 *         description: Task not found
 */

// Public project routes
router
  .route('/public')
  .get(validate(projectValidation.getProjects), projectController.getProjects);

// 新增：获取所有去重的建设单位（必须放在 /:projectId 之前）
router.get('/construction-units', projectController.getConstructionUnits);

// 获取项目列表用于下拉选择
router.get('/dropdown', projectController.getProjectsForDropdown);

// Project routes
router
  .route('/')
  .post(auth('manageProjects'), validate(projectValidation.createProject), projectController.createProject)
  .get(auth('getProjects'), validate(projectValidation.getProjects), projectController.getProjects);

router
  .route('/:projectId')
  .get(auth('getProjects'), validate(projectValidation.getProject), projectController.getProject)
  .patch(auth('manageProjects'), validate(projectValidation.updateProject), projectController.updateProject)
  .delete(auth('manageProjects'), validate(projectValidation.deleteProject), projectController.deleteProject);

// Project Progress routes
router
  .route('/:projectId/progress')
  .post(auth('manageProjects'), validate(projectValidation.createProjectProgress), projectProgressController.createProjectProgress)
  .get(auth('getProjects'), validate(projectValidation.getProjectProgressEntries), projectProgressController.getProjectProgressEntries);

router
  .route('/progress/:progressId')
  .get(auth('getProjects'), validate(projectValidation.getProjectProgress), projectProgressController.getProjectProgress)
  .patch(auth('manageProjects'), validate(projectValidation.updateProjectProgress), projectProgressController.updateProjectProgress)
  .delete(auth('manageProjects'), validate(projectValidation.deleteProjectProgress), projectProgressController.deleteProjectProgress);

// Project Task routes
router
  .route('/:projectId/tasks')
  .post(auth('manageProjects'), validate(projectValidation.createProjectTask), projectTaskController.createProjectTask)
  .get(auth('getProjects'), validate(projectValidation.getProjectTasks), projectTaskController.getProjectTasks);

router
  .route('/tasks/:taskId')
  .get(auth('getProjects'), validate(projectValidation.getProjectTask), projectTaskController.getProjectTask)
  .patch(auth('manageProjects'), validate(projectValidation.updateProjectTask), projectTaskController.updateProjectTask)
  .delete(auth('manageProjects'), validate(projectValidation.deleteProjectTask), projectTaskController.deleteProjectTask);

// Project Follow-up routes
router
  .route('/:projectId/follow-ups')
  .post(auth('manageProjects'), validate(projectValidation.createProjectFollowUp), projectFollowUpController.createProjectFollowUp)
  .get(auth('getProjects'), validate(projectValidation.getProjectFollowUps), projectFollowUpController.getProjectFollowUps);

router
  .route('/follow-ups/:followUpId')
  .get(auth('getProjects'), validate(projectValidation.getProjectFollowUp), projectFollowUpController.getProjectFollowUp)
  .patch(auth('manageProjects'), validate(projectValidation.updateProjectFollowUp), projectFollowUpController.updateProjectFollowUp)
  .delete(auth('manageProjects'), validate(projectValidation.deleteProjectFollowUp), projectFollowUpController.deleteProjectFollowUp);

// Project Contract routes
router
  .route('/:projectId/contracts')
  .get(auth('getContracts'), validate(contractValidation.getProjectContracts), contractController.getProjectContracts);

// Project Procurement routes
router
  .route('/:projectId/procurements')
  .get(auth('getProcurements'), validate(procurementValidation.getProjectProcurements), procurementController.getProjectProcurements);

module.exports = router;