# Database Configuration
DB_DIALECT=postgres
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yihe_db
DB_USER=postgres
DB_PASSWORD=your_password
DB_SCHEMA=public

# Database Pool Configuration
DB_POOL_MAX=5
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# Database Logging
DB_LOGGING=true

# Database SSL Configuration (if needed)
DB_SSL=false
DB_SSL_REJECT_UNAUTHORIZED=false
# DB_SSL_CA=

# Database Timeouts
DB_STATEMENT_TIMEOUT=10000
DB_IDLE_TIMEOUT=60000

# Application Name (for PostgreSQL)
APP_NAME=YiheApp 