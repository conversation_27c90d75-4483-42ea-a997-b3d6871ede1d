<template>
  <div class="page-container">
    <!-- 页面标题和按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">编辑项目</h1>
        <p class="page-subtitle">修改现有项目信息</p>
      </div>
      <router-link to="/projects" class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回列表
      </router-link>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误提示：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="card shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在获取项目信息，请稍候...</p>
      <p class="text-gray-500 text-sm mt-2">系统正在从数据库读取项目详细信息，这可能需要几秒钟时间</p>
    </div>

    <!-- 表单内容 -->
    <form v-if="!isLoading" @submit.prevent="saveProject" class="space-y-8 bg-white p-8 rounded-lg shadow">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- 左侧：基本信息和进度 -->
      <div class="md:col-span-2 space-y-8">
        <!-- 基本信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            基本信息
          </h2>
          <div class="form-group">
            <div class="form-grid">
              <div>
                <label for="name" class="form-label">项目名称 <span class="form-required">*</span></label>
                <input id="name" v-model="projectForm.name" type="text" class="form-input" required
                  placeholder="请输入项目名称" />
              </div>
              <div>
                <label for="code" class="form-label">项目编号</label>
                <input id="code" v-model="projectForm.code" type="text" class="form-input"
                  placeholder="自动生成或手动输入" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="status" class="form-label">项目状态</label>
                <select id="status" v-model="projectForm.status" class="form-input">
                  <option value="planning">规划中</option>
                  <option value="in_progress">进行中</option>
                  <option value="on_hold">已暂停</option>
                  <option value="completed">已完成</option>
                  <option value="cancelled">已取消</option>
                </select>
              </div>
              <div>
                <label for="manager" class="form-label">项目负责人 <span class="form-required">*</span></label>
                <select id="manager" v-model="projectForm.manager" class="form-input" required>
                  <option value="">请选择项目负责人</option>
                  <option v-for="user in userOptions" :key="user.id" :value="user.name">
                    {{ user.name }}
                  </option>
                </select>
              </div>
            </div>

            <div class="form-grid">
              <div class="relative">
                <label for="constructionUnit" class="form-label">建设单位</label>
                <input
                  id="constructionUnit"
                  v-model="constructionUnitInput"
                  @input="onConstructionUnitInput"
                  @focus="onConstructionUnitInput"
                  @blur="hideConstructionUnitSuggestions"
                  type="text"
                  class="form-input"
                  placeholder="请输入建设单位"
                  autocomplete="off"
                />
                <ul v-if="showConstructionUnitSuggestions && constructionUnitSuggestions.length" class="absolute z-10 bg-white border border-gray-200 rounded shadow w-full mt-1 max-h-40 overflow-auto">
                  <li
                    v-for="(suggestion, idx) in constructionUnitSuggestions"
                    :key="suggestion + idx"
                    @mousedown.prevent="selectConstructionUnitSuggestion(suggestion)"
                    class="px-3 py-2 cursor-pointer hover:bg-blue-100"
                  >
                    {{ suggestion }}
                  </li>
                </ul>
              </div>
              <div>
                <label for="designUnit" class="form-label">设计单位</label>
                <input id="designUnit" v-model="projectForm.designUnit" type="text" class="form-input"
                  placeholder="请输入设计单位" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="contractorUnit" class="form-label">施工单位</label>
                <input id="contractorUnit" v-model="projectForm.contractorUnit" type="text" class="form-input"
                  placeholder="请输入施工单位" />
              </div>
              <div>
                <label for="supervisorUnit" class="form-label">监理单位</label>
                <input id="supervisorUnit" v-model="projectForm.supervisorUnit" type="text" class="form-input"
                  placeholder="请输入监理单位" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="reviewUnit" class="form-label">审图单位</label>
                <input id="reviewUnit" v-model="projectForm.reviewUnit" type="text" class="form-input"
                  placeholder="请输入审图单位" />
              </div>
              <div>
                <label for="address" class="form-label">项目地址</label>
                <input id="address" v-model="projectForm.address" type="text" class="form-input"
                  placeholder="请输入项目地址" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="archiveNumber" class="form-label">项目档案号</label>
                <input id="archiveNumber" v-model="projectForm.archiveNumber" type="text" class="form-input"
                  placeholder="请输入项目档案号" />
              </div>
              <div>
                <label for="engineeringNumber" class="form-label">工程编号</label>
                <input id="engineeringNumber" v-model="projectForm.engineeringNumber" type="text" class="form-input"
                  placeholder="请输入工程编号" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="usageType" class="form-label">使用性质 <span class="form-required">*</span></label>
                <select id="usageType" v-model="projectForm.usageType" class="form-input" required>
                  <option value="">请选择</option>
                  <option value="commercial">商业</option>
                  <option value="residential">住宅</option>
                  <option value="industrial">工业</option>
                  <option value="public">公共建筑</option>
                </select>
              </div>
              <div>
                <label for="engineeringType" class="form-label">工程性质 <span class="form-required">*</span></label>
                <select id="engineeringType" v-model="projectForm.engineeringType" class="form-input" required>
                  <option value="">请选择</option>
                  <option value="building">建筑工程</option>
                  <option value="fire">消防工程</option>
                  <option value="annual">年度检测</option>
                  <option value="completion">竣工检测</option>
                  <option value="safety">消防安全评估</option>
                  <option value="maintenance">消防维保</option>
                </select>
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="constructionPermitNumber" class="form-label">施工许可证号</label>
                <input id="constructionPermitNumber" v-model="projectForm.constructionPermitNumber" type="text" class="form-input"
                  placeholder="请输入施工许可证号" />
              </div>
              <div>
                <label for="area" class="form-label">面积</label>
                <input id="area" v-model="projectForm.area" type="number" class="form-input"
                  placeholder="请输入面积" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="specialSystem" class="form-label">特殊系统</label>
                <input id="specialSystem" v-model="projectForm.specialSystem" type="text" class="form-input"
                  placeholder="请输入特殊系统" />
              </div>
              <div>
                <label for="companyManager" class="form-label">公司业务负责人</label>
                <input id="companyManager" v-model="projectForm.companyManager" type="text" class="form-input"
                  placeholder="请输入公司业务负责人" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="clientContact" class="form-label">甲方项目对接人</label>
                <input id="clientContact" v-model="projectForm.clientContact" type="text" class="form-input"
                  placeholder="请输入甲方项目对接人" />
              </div>
            </div>

            <div>
              <label for="description" class="form-label">项目描述</label>
              <textarea id="description" v-model="projectForm.description" rows="3" class="form-input"
                placeholder="输入项目的简要描述信息..."></textarea>
            </div>

            <div>
              <label for="project-tags" class="form-label">项目标签</label>
              <div id="project-tags" class="flex flex-wrap gap-2 mt-2">
                <div v-for="(tag, index) in availableTags" :key="tag"
                  @click="toggleTag(tag)"
                  :class="[
                    'cursor-pointer px-3 py-1 rounded-full text-sm transition-colors duration-200',
                    projectForm.tags.includes(tag)
                      ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  ]">
                  {{ tag }}
                </div>
                <div class="relative">
                  <input
                    v-model="newTag"
                    @keydown.enter.prevent="addNewTag"
                    type="text"
                    placeholder="+ 添加新标签"
                    class="form-input p-1.5 text-sm min-w-[120px] max-w-[200px]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 时间信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            时间信息
          </h2>
          <div class="form-group">
            <div class="form-grid">
              <div>
                <label for="startDate" class="form-label">开始日期 <span class="form-required">*</span></label>
                <input id="startDate" v-model="projectForm.startDate" type="date" class="form-input" required max="3000-12-31" />
              </div>
              <div>
                <label for="endDate" class="form-label">计划结束日期 <span class="form-required">*</span></label>
                <input id="endDate" v-model="projectForm.endDate" type="date" class="form-input" required max="3000-12-31" />
              </div>
            </div>

            <div>
              <label for="progress" class="form-label">当前进度</label>
              <div class="flex items-center space-x-3">
                <input
                  id="progress"
                  type="range"
                  min="0"
                  max="100"
                  step="5"
                  v-model="projectForm.progress"
                  class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <span class="font-medium" :class="getProgressTextColor(projectForm.progress)">{{ projectForm.progress }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                <div class="h-2.5 rounded-full" :class="getProgressBarColor(projectForm.progress)" :style="{ width: `${projectForm.progress}%` }"></div>
              </div>
            </div>

            <div>
              <label for="reminders" class="form-label">重要事项提醒</label>
              <textarea id="reminders" v-model="projectForm.reminders" rows="2" class="form-input"
                placeholder="记录需要提醒的重要事项..."></textarea>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：详细信息 -->
      <div class="space-y-8">
        <!-- 项目细节卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            项目细节
          </h2>
          <div class="form-group">
            <div>
              <label for="designDirection" class="form-label">设计方向</label>
              <input id="designDirection" v-model="projectForm.designDirection" type="text" class="form-input"
                placeholder="请输入设计方向" />
            </div>
            <div>
              <label for="constructionDirection" class="form-label">施工方向</label>
              <input id="constructionDirection" v-model="projectForm.constructionDirection" type="text" class="form-input"
                placeholder="请输入施工方向" />
            </div>
            <div>
              <label for="supervisor" class="form-label">监理单位</label>
              <input id="supervisor" v-model="projectForm.supervisor" type="text" class="form-input"
                placeholder="请输入监理单位" />
            </div>
          </div>
        </div>

        <!-- 项目团队卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            项目团队
          </h2>
          <div class="form-group">
            <div v-for="(member, index) in projectForm.teamMembers" :key="index" class="flex items-center gap-3">
              <select
                v-model="projectForm.teamMembers[index].id"
                class="form-input p-2.5 transition-colors duration-200 flex-1 min-w-[180px] max-w-xs"
              >
                <option value="">请选择团队成员</option>
                <option v-for="user in userOptions" :key="user.id" :value="user.id">{{ user.name }}</option>
              </select>
              <button
                @click="removeTeamMember(index)"
                class="text-red-500 hover:text-red-700 transition-colors duration-200"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="2.5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
            <button
              @click="addTeamMember"
              class="btn btn-secondary w-full text-center py-2"
            >
              <svg class="w-5 h-5 inline-block mr-1" fill="none" stroke="currentColor" stroke-width="2.5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              添加团队成员
            </button>
          </div>
        </div>
      </div>
      </div>
      
      <!-- 表单操作按钮 -->
      <div class="flex justify-end space-x-4 pt-6 border-t">
        <button type="button" @click="goBack" class="btn btn-secondary shadow-md hover:shadow-lg transition-all duration-200 px-5 py-2">
          <svg class="w-5 h-5 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回
        </button>
        <button type="submit" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200 px-6 py-2" :disabled="isSaving">
          <svg v-if="isSaving" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else class="w-5 h-5 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M5 13l4 4L19 7"></path>
          </svg>
          保存
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import apiService from '@/services/apiService';

const router = useRouter();
const route = useRoute();
const error = ref('');
const isSaving = ref(false);
const isLoading = ref(true);
const newTag = ref('');
const userOptions = ref([]);

// 建设单位自动补全相关
const constructionUnitSuggestions = ref([]);
const showConstructionUnitSuggestions = ref(false);
const constructionUnitInput = ref('');
let constructionUnitFetchTimeout = null;

// 获取用户列表
const fetchUsers = async () => {
  try {
    const users = await apiService.users.getUsers();
    userOptions.value = users;
    return users;
  } catch (err) {
    console.error('获取用户列表失败:', err);
    error.value = '获取用户列表失败，请刷新页面重试';
    throw err;
  }
};

// 可选标签
const availableTags = [
  '设计', '规划', '商业', '住宅', '工业', '改造',
  '景观', '教育', '医疗', '交通', '文化', '体育'
];

// 表单数据
const projectForm = reactive({
  name: '',
  code: '',
  status: 'planning',
  manager: '',
  description: '',
  startDate: '',
  endDate: '',
  progress: 0,
  designDirection: '',
  constructionDirection: '',
  supervisor: '',
  reminders: '',
  tags: [],
  teamMembers: [
    { id: '' }
  ],
  constructionUnit: '',
  designUnit: '',
  contractorUnit: '',
  supervisorUnit: '',
  reviewUnit: '',
  address: '',
  archiveNumber: '',
  engineeringNumber: '',
  usageType: '',
  engineeringType: '',
  constructionPermitNumber: '',
  area: '',
  specialSystem: '',
  companyManager: '',
  clientContact: ''
});

// 获取项目信息
async function fetchProject() {
  try {
    const projectId = route.params.id;
    if (!projectId) {
      error.value = '项目ID不能为空';
      isLoading.value = false;
      return null;
    }

    const response = await axios.get(`/api/projects/${projectId}`);
    const project = response.data;

    if (!project) {
      error.value = '项目不存在';
      isLoading.value = false;
      return null;
    }

    // 更新表单数据
    Object.assign(projectForm, project);
    
    // 处理团队成员数据
    if (project.members) {
      let memberIds = [];
      
      // 如果 members 是字符串，尝试解析为 JSON
      if (typeof project.members === 'string') {
        try {
          memberIds = JSON.parse(project.members);
        } catch {
          memberIds = project.members.split(',').filter(Boolean);
        }
      } else if (Array.isArray(project.members)) {
        memberIds = project.members;
      }
      
      // 将成员 ID 转换为团队成员对象
      if (memberIds.length > 0) {
        projectForm.teamMembers = memberIds.map(id => ({ id }));
      } else {
        projectForm.teamMembers = [{ id: '' }];
      }
    } else {
      projectForm.teamMembers = [{ id: '' }];
    }
    
    return project;
  } catch (err) {
    console.error('获取项目信息失败:', err);
    error.value = err.response?.data?.message || '获取项目信息失败，请重试';
    throw err;
  } finally {
    isLoading.value = false;
  }
}

// 组件挂载时先获取用户列表，然后获取项目信息
onMounted(async () => {
  try {
    await fetchUsers();
    await fetchProject();
  } catch (err) {
    console.error('初始化页面失败:', err);
    error.value = '加载数据失败，请刷新页面重试';
  }
});

// 允许的字段列表
const allowedUpdateFields = [
  'name', 'members', 'manager', 'constructionUnit', 'designUnit', 'contractorUnit', 'supervisorUnit', 'reviewUnit',
  'address', 'startDate', 'endDate', 'archiveNumber', 'engineeringNumber', 'usageType', 'engineeringType',
  'constructionPermitNumber', 'area', 'specialSystem', 'companyManager', 'status', 'progress', 'clientContact', 'tags'
];

function filterProjectUpdatePayload(form) {
  const payload = {};
  for (const key of allowedUpdateFields) {
    if (form[key] !== undefined) payload[key] = form[key];
  }
  // reviewUnit 如为数组或对象，转为字符串
  if (typeof payload.reviewUnit !== 'string' && payload.reviewUnit !== undefined) {
    payload.reviewUnit = String(payload.reviewUnit);
  }
  return payload;
}

// 保存项目信息
async function saveProject() {
  // 表单验证
  if (!projectForm.name) {
    error.value = '请输入项目名称';
    return;
  }

  if (!projectForm.manager) {
    error.value = '请输入项目负责人';
    return;
  }

  if (!projectForm.startDate) {
    error.value = '请选择开始日期';
    return;
  }

  if (!projectForm.endDate) {
    error.value = '请选择计划结束日期';
    return;
  }

  // 验证日期大小关系
  if (new Date(projectForm.endDate) < new Date(projectForm.startDate)) {
    error.value = '计划结束日期不能早于开始日期';
    return;
  }

  isSaving.value = true;
  error.value = '';

  try {
    const projectId = route.params.id;
    if (!projectId) {
      error.value = '项目ID不能为空';
      return;
    }
    // 移除空的团队成员
    projectForm.teamMembers = projectForm.teamMembers.filter(member => member.id !== '');
    // 只提交允许的字段
    const payload = filterProjectUpdatePayload(projectForm);
    // 同步 teamMembers 到 members 字段
    const memberIds = Array.isArray(projectForm.teamMembers)
      ? projectForm.teamMembers.map(member => member.id).filter(Boolean)
      : [];
    // 直接使用用户ID数组，因为后端验证和模型都期望 members 是 UUID 数组
    payload.members = memberIds.length > 0 ? memberIds : [];
    // 保证 tags 字段为数组
    if (payload.tags && typeof payload.tags === 'string') {
      try {
        payload.tags = JSON.parse(payload.tags);
      } catch {
        payload.tags = payload.tags.split(',').map(t => t.trim()).filter(Boolean);
      }
    }
    if (!Array.isArray(payload.tags)) {
      payload.tags = [];
    }
    // 更新项目信息
    await apiService.updateProject(projectId, payload);
    // 返回项目列表页
    router.push('/projects');
  } catch (err) {
    console.error('更新项目失败:', err);
    error.value = err.response?.data?.message || '更新项目失败，请重试';
  } finally {
    isSaving.value = false;
  }
}

// 添加新标签
function addNewTag() {
  if (newTag.value && !projectForm.tags.includes(newTag.value)) {
    projectForm.tags.push(newTag.value);
    newTag.value = '';
  }
}

// 切换标签选中状态
function toggleTag(tag) {
  const index = projectForm.tags.indexOf(tag);
  if (index === -1) {
    projectForm.tags.push(tag);
  } else {
    projectForm.tags.splice(index, 1);
  }
}

// 添加团队成员
function addTeamMember() {
  projectForm.teamMembers.push({ id: '' });
}

// 移除团队成员
function removeTeamMember(index) {
  projectForm.teamMembers.splice(index, 1);
  // 确保至少有一个成员选择框
  if (projectForm.teamMembers.length === 0) {
    addTeamMember();
  }
}

// 获取进度条颜色
function getProgressBarColor(progress) {
  if (progress < 25) return 'bg-red-600';
  if (progress < 50) return 'bg-yellow-600';
  if (progress < 75) return 'bg-blue-600';
  return 'bg-green-600';
}

// 获取进度文本颜色
function getProgressTextColor(progress) {
  if (progress < 25) return 'text-red-600';
  if (progress < 50) return 'text-yellow-600';
  if (progress < 75) return 'text-blue-600';
  return 'text-green-600';
}

// 自动补全：输入时获取建议
async function onConstructionUnitInput(e) {
  const value = e.target.value;
  projectForm.constructionUnit = value;
  constructionUnitInput.value = value;
  if (constructionUnitFetchTimeout) clearTimeout(constructionUnitFetchTimeout);
  if (!value) {
    constructionUnitSuggestions.value = [];
    showConstructionUnitSuggestions.value = false;
    return;
  }
  constructionUnitFetchTimeout = setTimeout(async () => {
    try {
      const suggestions = await apiService.getConstructionUnits(value);
      constructionUnitSuggestions.value = suggestions;
      showConstructionUnitSuggestions.value = suggestions.length > 0;
    } catch (err) {
      constructionUnitSuggestions.value = [];
      showConstructionUnitSuggestions.value = false;
    }
  }, 200);
}

function selectConstructionUnitSuggestion(suggestion) {
  projectForm.constructionUnit = suggestion;
  constructionUnitInput.value = suggestion;
  showConstructionUnitSuggestions.value = false;
}

function hideConstructionUnitSuggestions() {
  setTimeout(() => { showConstructionUnitSuggestions.value = false; }, 150);
}

// 组件挂载时获取项目信息
onMounted(() => {
  fetchProject();
});
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}
.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}
.page-header {
  @apply flex justify-between items-center mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}
.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}
.form-section {
  @apply mb-8;
}
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}
.form-input {
  @apply mt-1 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 rounded-md;
}
.btn {
  @apply inline-flex justify-center py-2 px-4 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2;
}
.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 border-transparent focus:ring-blue-500;
}
.btn-secondary {
  @apply text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-blue-500;
}

/* 标签动画 */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* 进度条样式 */
input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #2563eb;
  border-radius: 50%;
  cursor: pointer;
}

input[type=range]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #2563eb;
  border-radius: 50%;
  cursor: pointer;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>