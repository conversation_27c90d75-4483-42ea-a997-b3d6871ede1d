const Joi = require('joi');

// Project validations
const createProject = {
  body: Joi.object().keys({
    code: Joi.string().allow(null, '').messages({
      'string.empty': '项目编码不能为空'
    }),
    name: Joi.string().required().messages({
      'string.empty': '项目名称不能为空',
      'any.required': '项目名称是必填项'
    }),
    members: Joi.array().items(Joi.string().uuid()).allow(null),
    manager: Joi.string().allow(null),
    constructionUnit: Joi.string().allow(null),
    designUnit: Joi.string().allow(null),
    contractorUnit: Joi.string().allow(null),
    supervisorUnit: Joi.string().allow(null),
    reviewUnit: Joi.string().allow(null),
    address: Joi.string().allow(null),
    startDate: Joi.date().messages({
      'date.base': '开始日期格式不正确'
    }),
    endDate: Joi.date().min(Joi.ref('startDate')).messages({
      'date.base': '结束日期格式不正确',
      'date.min': '结束日期必须晚于开始日期'
    }),
    archiveNumber: Joi.string().allow(null),
    engineeringNumber: Joi.string().allow(null),
    usageType: Joi.string().required().messages({
      'string.empty': '用途类型不能为空',
      'any.only': '用途类型必须是商业、住宅、工业或公共建筑之一',
      'any.required': '用途类型是必填项'
    }),
    engineeringType: Joi.string().required().messages({
      'string.empty': '工程类型不能为空',
      'any.only': '工程类型必须是建筑工程、消防工程、年度检测、竣工检测、消防安全评估或消防维保之一',
      'any.required': '工程类型是必填项'
    }),
    constructionPermitNumber: Joi.string().allow(null),
    area: Joi.number().min(0).messages({
      'number.base': '面积必须是数字',
      'number.min': '面积不能小于0'
    }),
    specialSystem: Joi.string().allow(null),
    companyManager: Joi.string().allow(null),
    status: Joi.string().valid('planning', 'in_progress', 'completed', 'on_hold', 'cancelled').default('planning').messages({
      'any.only': '状态必须是规划中(planning)、进行中(in_progress)、已完成(completed)、暂停(on_hold)或已取消(cancelled)'
    }),
    progress: Joi.number().integer().min(0).max(100).default(0).messages({
      'number.base': '进度必须是数字',
      'number.integer': '进度必须是整数',
      'number.min': '进度不能小于0',
      'number.max': '进度不能超过100'
    }),
    clientContact: Joi.string().allow(null),
    tags: Joi.array().items(Joi.string())
  })
};

const getProjects = {
  query: Joi.object().keys({
    name: Joi.string().allow(null),
    status: Joi.string().valid('planning', 'in_progress', 'completed', 'on_hold', 'cancelled').allow(null, ''),
    usageType: Joi.string().valid('商业', '住宅', '工业', '公共建筑').allow(null, ''),
    engineeringType: Joi.string().valid('建筑工程', '消防工程', '年度检测', '竣工检测', '消防安全评估', '消防维保').allow(null, ''),
    startDate: Joi.date(),
    endDate: Joi.date(),
    tags: Joi.array().items(Joi.string()),
    search: Joi.string().allow(null, ''),
    manager: Joi.string().allow(null, ''),
    sortBy: Joi.string().valid('name', 'createdAt', 'startDate', 'endDate', 'progress').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100000).default(10)
  })
};

const getProject = {
  params: Joi.object().keys({
    projectId: Joi.string().uuid().required().messages({
      'string.guid': '项目ID必须是有效的UUID格式',
      'string.empty': '项目ID不能为空',
      'any.required': '项目ID是必填项'
    })
  })
};

const updateProject = {
  params: Joi.object().keys({
    projectId: Joi.string().uuid().required().messages({
      'string.guid': '项目ID必须是有效的UUID格式',
      'string.empty': '项目ID不能为空',
      'any.required': '项目ID是必填项'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().allow(null),
    members: Joi.array().items(Joi.string().uuid()).allow(null),
    manager: Joi.string().allow(null),
    constructionUnit: Joi.string().allow(null),
    designUnit: Joi.string().allow(null),
    contractorUnit: Joi.string().allow(null),
    supervisorUnit: Joi.string().allow(null),
    reviewUnit: Joi.string().allow(null),
    address: Joi.string().allow(null),
    startDate: Joi.date(),
    endDate: Joi.date().min(Joi.ref('startDate')),
    archiveNumber: Joi.string().allow(null),
    engineeringNumber: Joi.string().allow(null),
    usageType: Joi.string().allow(null),
    engineeringType: Joi.string().allow(null),
    constructionPermitNumber: Joi.string().allow(null),
    area: Joi.number().min(0).allow(null),
    specialSystem: Joi.string().allow(null),
    companyManager: Joi.string().allow(null),
    status: Joi.string().valid('planning', 'in_progress', 'completed', 'on_hold', 'cancelled'),
    progress: Joi.number().integer().min(0).max(100),
    clientContact: Joi.string().allow(null),
    tags: Joi.array().items(Joi.string())
  }).min(1)
};

const deleteProject = {
  params: Joi.object().keys({
    projectId: Joi.string().uuid().required().messages({
      'string.guid': '项目ID必须是有效的UUID格式',
      'string.empty': '项目ID不能为空',
      'any.required': '项目ID是必填项'
    })
  })
};

// Project Progress validations
const createProjectProgress = {
  params: Joi.object().keys({
    projectId: Joi.string().uuid().required().messages({
      'string.guid': '项目ID必须是有效的UUID格式',
      'string.empty': '项目ID不能为空',
      'any.required': '项目ID是必填项'
    })
  }),
  body: Joi.object().keys({
    stage: Joi.string().required().messages({
      'string.empty': '阶段不能为空',
      'any.required': '阶段是必填项'
    }),
    completionPercentage: Joi.number().min(0).max(100).required().messages({
      'number.base': '完成百分比必须是数字',
      'number.min': '完成百分比不能小于0',
      'number.max': '完成百分比不能超过100',
      'any.required': '完成百分比是必填项'
    }),
    currentStatus: Joi.string().allow(null, ''),
    deliveryDate: Joi.date().allow(null).messages({
      'date.base': '交付日期格式不正确'
    }),
    delayStatus: Joi.boolean().messages({
      'boolean.base': '延迟状态必须是布尔值'
    }),
    delayReason: Joi.string().allow(null, '')
  })
};

const getProjectProgress = {
  params: Joi.object().keys({
    progressId: Joi.string().uuid().required().messages({
      'string.guid': '进度ID必须是有效的UUID格式',
      'string.empty': '进度ID不能为空',
      'any.required': '进度ID是必填项'
    })
  })
};

const updateProjectProgress = {
  params: Joi.object().keys({
    progressId: Joi.string().uuid().required()
  }),
  body: Joi.object().keys({
    stage: Joi.string().allow(null),
    completionPercentage: Joi.number().min(0).max(100),
    currentStatus: Joi.string().allow(null, ''),
    deliveryDate: Joi.date().allow(null),
    delayStatus: Joi.boolean(),
    delayReason: Joi.string().allow(null, '')
  }).min(1)
};

const deleteProjectProgress = {
  params: Joi.object().keys({
    progressId: Joi.string().uuid().required()
  })
};

const getProjectProgressEntries = {
  params: Joi.object().keys({
    projectId: Joi.string().uuid().required()
  }),
  query: Joi.object().keys({
    stage: Joi.string().allow(null),
    delayStatus: Joi.boolean(),
    page: Joi.number().integer(),
    limit: Joi.number().integer().min(1).max(100000).default(10),
    sortBy: Joi.string().allow(null),
    sortOrder: Joi.string().valid('asc', 'desc')
  })
};

// Project Task validations
const createProjectTask = {
  params: Joi.object().keys({
    projectId: Joi.string().uuid().required().messages({
      'string.guid': '项目ID必须是有效的UUID格式',
      'string.empty': '项目ID不能为空',
      'any.required': '项目ID是必填项'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().required().messages({
      'string.empty': '任务名称不能为空',
      'any.required': '任务名称是必填项'
    }),
    taskType: Joi.string().required().messages({
      'string.empty': '任务类型不能为空',
      'any.required': '任务类型是必填项'
    }),
    status: Joi.string().valid('not_started', 'in_progress', 'completed', 'on_hold', 'cancelled').messages({
      'any.only': '状态必须是未开始(not_started)、进行中(in_progress)、已完成(completed)、暂停(on_hold)或已取消(cancelled)'
    }),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent').messages({
      'any.only': '优先级必须是低(low)、中(medium)、高(high)或紧急(urgent)'
    }),
    assigneeId: Joi.string().uuid().required().messages({
      'string.guid': '负责人ID必须是有效的UUID格式',
      'string.empty': '负责人ID不能为空',
      'any.required': '负责人ID是必填项'
    }),
    plannedStartDate: Joi.date().required().messages({
      'date.base': '计划开始日期格式不正确',
      'any.required': '计划开始日期是必填项'
    }),
    plannedEndDate: Joi.date().required().messages({
      'date.base': '计划结束日期格式不正确',
      'any.required': '计划结束日期是必填项'
    }),
    actualStartDate: Joi.date().allow(null).messages({
      'date.base': '实际开始日期格式不正确'
    }),
    actualEndDate: Joi.date().allow(null).messages({
      'date.base': '实际结束日期格式不正确'
    }),
    remainingWork: Joi.number().min(0).messages({
      'number.base': '剩余工作量必须是数字',
      'number.min': '剩余工作量不能小于0'
    }),
    description: Joi.string().allow(null, '')
  })
};

const getProjectTask = {
  params: Joi.object().keys({
    taskId: Joi.string().uuid().required()
  })
};

const updateProjectTask = {
  params: Joi.object().keys({
    taskId: Joi.string().uuid().required()
  }),
  body: Joi.object().keys({
    name: Joi.string().allow(null),
    taskType: Joi.string().allow(null),
    status: Joi.string().valid('not_started', 'in_progress', 'completed', 'on_hold', 'cancelled'),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent'),
    assigneeId: Joi.string().uuid(),
    plannedStartDate: Joi.date(),
    plannedEndDate: Joi.date(),
    actualStartDate: Joi.date().allow(null),
    actualEndDate: Joi.date().allow(null),
    remainingWork: Joi.number().min(0),
    description: Joi.string().allow(null, '')
  }).min(1)
};

const deleteProjectTask = {
  params: Joi.object().keys({
    taskId: Joi.string().uuid().required()
  })
};

const getProjectTasks = {
  params: Joi.object().keys({
    projectId: Joi.string().uuid().required()
  }),
  query: Joi.object().keys({
    status: Joi.string().valid('not_started', 'in_progress', 'completed', 'on_hold', 'cancelled'),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent'),
    assigneeId: Joi.string().uuid(),
    taskType: Joi.string().allow(null),
    search: Joi.string().allow(null),
    page: Joi.number().integer(),
    limit: Joi.number().integer().min(1).max(100000).default(10),
    sortBy: Joi.string().allow(null),
    sortOrder: Joi.string().valid('asc', 'desc')
  })
};

// Project Follow-up validations
const createProjectFollowUp = {
  params: Joi.object().keys({
    projectId: Joi.string().uuid().required().messages({
      'string.guid': '项目ID必须是有效的UUID格式',
      'string.empty': '项目ID不能为空',
      'any.required': '项目ID是必填项'
    })
  }),
  body: Joi.object().keys({
    followUpDate: Joi.date().required().messages({
      'date.base': '跟进日期格式不正确',
      'any.required': '跟进日期是必填项'
    }),
    content: Joi.string().required().messages({
      'string.empty': '跟进内容不能为空',
      'any.required': '跟进内容是必填项'
    })
  })
};

const getProjectFollowUp = {
  params: Joi.object().keys({
    followUpId: Joi.string().uuid().required().messages({
      'string.guid': '跟进ID必须是有效的UUID格式',
      'string.empty': '跟进ID不能为空',
      'any.required': '跟进ID是必填项'
    })
  })
};

const updateProjectFollowUp = {
  params: Joi.object().keys({
    followUpId: Joi.string().uuid().required()
  }),
  body: Joi.object().keys({
    followUpDate: Joi.date(),
    content: Joi.string().allow(null)
  }).min(1)
};

const deleteProjectFollowUp = {
  params: Joi.object().keys({
    followUpId: Joi.string().uuid().required()
  })
};

const getProjectFollowUps = {
  params: Joi.object().keys({
    projectId: Joi.string().uuid().required()
  }),
  query: Joi.object().keys({
    startDate: Joi.date(),
    endDate: Joi.date(),
    page: Joi.number().integer(),
    limit: Joi.number().integer().min(1).max(100000).default(10),
    sortBy: Joi.string().allow(null),
    sortOrder: Joi.string().valid('asc', 'desc')
  })
};

module.exports = {
  createProject,
  getProjects,
  getProject,
  updateProject,
  deleteProject,
  createProjectProgress,
  getProjectProgress,
  updateProjectProgress,
  deleteProjectProgress,
  getProjectProgressEntries,
  createProjectTask,
  getProjectTask,
  updateProjectTask,
  deleteProjectTask,
  getProjectTasks,
  createProjectFollowUp,
  getProjectFollowUp,
  updateProjectFollowUp,
  deleteProjectFollowUp,
  getProjectFollowUps
};