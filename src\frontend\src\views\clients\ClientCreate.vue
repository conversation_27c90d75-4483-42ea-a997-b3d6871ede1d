<template>
  <div class="page-container">
    <!-- 页面标题和按钮 -->
    <div class="page-header border-b border-gray-200 pb-4 mb-6">
      <div class="flex items-center">
        <h1 class="page-title">新建客户</h1>
      </div>
      <div class="flex space-x-3">
        <router-link to="/clients" class="btn btn-secondary shadow-sm hover:shadow-md transition-all duration-200">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>         
          返回列表
        </router-link>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-alert">
      <div class="flex">
        <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        {{ error }}
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- 左侧：基本信息 -->
      <div class="md:col-span-2 space-y-8">
        <!-- 基本信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            基本信息
          </h2>
          <div class="form-group">
            <div class="form-grid">
              <div>
                <label for="name" class="form-label">客户名称 <span class="text-red-500">*</span></label>
                <input id="name" v-model="clientForm.name" type="text" class="form-input p-2.5 transition-colors duration-200" required
                  placeholder="请输入客户名称" autocomplete="off" />
              </div>
              <div>
                <label for="code" class="form-label">客户编号 <span class="form-required">*</span></label>
                <input id="code" v-model="clientForm.code" type="text" class="form-input p-2.5 transition-colors duration-200"
                  placeholder="系统将自动生成编号，您也可以自行修改" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="type" class="form-label">客户类型</label>
                <select id="type" v-model="clientForm.type" class="form-input">
                  <option value="">请选择类型</option>
                  <option value="company">企业客户</option>
                  <option value="individual">个人客户</option>
                  <option value="government">政府单位</option>
                </select>
              </div>
              <div>
                <label for="status" class="form-label">客户状态</label>
                <select id="status" v-model="clientForm.status" class="form-input">
                  <option value="">请选择状态</option>
                  <option value="active">活跃</option>
                  <option value="inactive">非活跃</option>
                  <option value="potential">潜在</option>
                </select>
              </div>
            </div>

            <div>
              <label for="description" class="form-label">客户描述</label>
              <textarea id="description" v-model="clientForm.description" rows="3" class="form-input"
                placeholder="输入客户的简要描述信息..."></textarea>
            </div>
          </div>
        </div>

        <!-- 联系信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
            </svg>
            联系信息
          </h2>
          <div class="form-group">
            <div class="form-grid">
              <div>
                <label for="contactName" class="form-label">联系人姓名</label>
                <input id="contactName" v-model="clientForm.contactName" type="text" class="form-input"
                  placeholder="请输入联系人姓名" />
              </div>
              <div>
                <label for="contactTitle" class="form-label">联系人职位</label>
                <input id="contactTitle" v-model="clientForm.contactTitle" type="text" class="form-input"
                  placeholder="请输入联系人职位" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="phone" class="form-label">联系电话</label>
                <input id="phone" v-model="clientForm.phone" type="tel" class="form-input"
                  placeholder="请输入联系电话" />
              </div>
              <div>
                <label for="email" class="form-label">电子邮箱</label>
                <input id="email" v-model="clientForm.email" type="email" class="form-input"
                  placeholder="请输入电子邮箱" />
              </div>
            </div>

            <div>
              <label for="address" class="form-label">地址</label>
              <textarea id="address" v-model="clientForm.address" rows="2" class="form-input"
                placeholder="请输入详细地址信息..."></textarea>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：附加信息 -->
      <div class="space-y-8">
        <!-- 财务信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            财务信息
          </h2>
          <div class="form-group">
            <div>
              <label for="taxId" class="form-label">税号</label>
              <input id="taxId" v-model="clientForm.taxId" type="text" class="form-input"
                placeholder="请输入税号" />
            </div>
            <div>
              <label for="bankName" class="form-label">开户银行</label>
              <input id="bankName" v-model="clientForm.bankName" type="text" class="form-input"
                placeholder="请输入开户银行" />
            </div>
            <div>
              <label for="bankCode" class="form-label">联行号</label>
              <input id="bankCode" v-model="clientForm.bankCode" type="text" class="form-input"
                placeholder="请输入联行号" />
            </div>
            <div>
              <label for="bankAccount" class="form-label">银行账户</label>
              <input id="bankAccount" v-model="clientForm.bankAccount" type="text" class="form-input"
                placeholder="请输入银行账户" />
            </div>
          </div>
        </div>

        <!-- 其他信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            其他信息
          </h2>
          <div class="form-group">
            <div>
              <label for="source" class="form-label">客户来源</label>
              <select id="source" v-model="clientForm.source" class="form-input">
                <option value="">请选择来源</option>
                <option value="referral">推荐</option>
                <option value="website">网站</option>
                <option value="advertisement">广告</option>
                <option value="exhibition">展会</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div>
              <label for="industry" class="form-label">所属行业</label>
              <select id="industry" v-model="clientForm.industry" class="form-input">
                <option value="">请选择行业</option>
                <option value="technology">科技</option>
                <option value="manufacturing">制造业</option>
                <option value="finance">金融</option>
                <option value="healthcare">医疗健康</option>
                <option value="education">教育</option>
                <option value="retail">零售</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div>
              <label for="notes" class="form-label">备注</label>
              <textarea id="notes" v-model="clientForm.notes" rows="3" class="form-input"
                placeholder="输入需要特别注意的事项..."></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部保存按钮 -->
    <div class="mt-8 flex justify-end">
      <button @click="saveClient" class="btn btn-primary shadow-sm hover:shadow-md transition-all duration-200" :disabled="isSaving">
        <svg v-if="isSaving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        创建
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import apiService from '@/services/apiService';

const router = useRouter();

// 状态变量
const error = ref('');
const isSaving = ref(false);

// 表单数据
const clientForm = reactive({
  name: '',
  code: '',
  type: '',
  status: '',
  description: '',
  contactName: '',
  contactTitle: '',
  phone: '',
  email: '',
  address: '',
  taxId: '',
  bankAccount: '',
  bankName: '',
  bankCode: '',
  source: '',
  industry: '',
  notes: ''
});

// 生成客户编号
function generateClientNumber() {
  const date = new Date();
  const year = date.getFullYear().toString().slice(2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `CL-${year}${month}${day}-${random}`;
}

// 自动生成客户编号
clientForm.code = generateClientNumber();

// 保存客户信息
async function saveClient() {
  isSaving.value = true;
  error.value = '';

  try {
    // 表单验证
    if (!clientForm.name) {
      throw new Error('请输入客户名称。客户名称是必填项，用于在系统中唯一标识客户，请提供有效的客户名称后再次提交。');
    }
    
    if (!clientForm.code) {
      throw new Error('请输入客户编号。客户编号是必填项，请提供有效的客户编号后再次提交。');
    }

    // 如果没有设置客户编号，自动生成一个
    if (!clientForm.code) {
      clientForm.code = generateClientNumber();
    }

    // 准备提交的数据
    const clientData = { ...clientForm };

    // 调用API创建客户
    await apiService.createClient(clientData);

    // 保存成功后返回客户列表页，并添加refresh参数以触发自动刷新
    router.push({ path: '/clients', query: { refresh: true } });
  } catch (err) {
    console.error('创建客户失败:', err);

    // 处理API返回的错误
    if (err.response && err.response.data) {
      if (err.response.data.message) {
        error.value = err.response.data.message;
      } else if (err.response.data.errors) {
        // 处理验证错误
        const validationErrors = err.response.data.errors;
        const errorMessages = Object.values(validationErrors).flat();
        error.value = errorMessages.join('\n');
      } else {
        error.value = '很抱歉，创建客户时遇到了问题。这可能是由于网络连接问题或系统临时故障导致的。请稍后再次尝试，如果问题持续存在，请联系系统管理员获取帮助。';
      }
    } else {
      error.value = err.message || '很抱歉，创建客户时遇到了问题。这可能是由于网络连接问题或系统临时故障导致的。请检查您的输入信息并稍后再试，如果问题持续存在，请联系系统管理员获取帮助。';
    }
  } finally {
    isSaving.value = false;
  }
}
</script>