<template>
  <div class="max-w-6xl mx-auto">
    <!-- 项目头部信息 -->
    <div v-if="loading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-2 text-gray-600">加载项目信息中...</p>
    </div>

    <div v-else-if="error" class="card text-center py-12">
      <svg class="w-16 h-16 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <h3 class="mt-4 text-xl font-medium text-gray-900">加载错误</h3>
      <p class="mt-2 text-gray-600">{{ error }}</p>
      <div class="mt-6">
        <button @click="fetchProjectDetail()" class="btn btn-primary">
          重试
        </button>
        <router-link to="/projects" class="btn btn-secondary ml-2">
          返回项目列表
        </router-link>
      </div>
    </div>

    <div v-else-if="!project" class="card text-center py-12">
      <svg class="w-16 h-16 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
      </svg>
      <h3 class="mt-4 text-xl font-medium text-gray-900">项目不存在</h3>
      <p class="mt-2 text-gray-600">您查找的项目不存在或已被删除</p>
      <div class="mt-6">
        <router-link to="/projects" class="btn btn-primary">
          返回项目列表
        </router-link>
      </div>
    </div>

    <div v-else>
      <!-- 项目头部 -->
      <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div class="flex-grow">
          <div class="flex items-center mb-2">
            <router-link to="/projects" class="text-blue-600 hover:text-blue-800 mr-2">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
            </router-link>
            <span :class="[`badge`, getStatusClass(project.status)]">{{ getStatusText(project.status) }}</span>
          </div>
          <h1 class="page-title">{{ project.name }}</h1>
          <p class="text-sm text-gray-500 mt-1">项目编号: {{ project.code }}</p>
        </div>
        <div class="mt-4 md:mt-0 flex items-center space-x-3">
          <router-link :to="`/projects/edit/${project.id}`" class="btn btn-secondary">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            编辑
          </router-link>
          <button @click="confirmDelete" class="btn btn-danger">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            删除
          </button>
        </div>
      </div>

      <!-- 项目概览 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="card p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
              <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-500">进度</div>
              <div class="text-lg font-semibold text-gray-900">{{ project.progress }}%</div>
            </div>
          </div>
          <div class="mt-3 w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full" :style="{ width: `${project.progress}%` }"></div>
          </div>
        </div>

        <div class="card p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
              <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-500">开始日期</div>
              <div class="text-lg font-semibold text-gray-900">{{ formatDate(project.startDate) }}</div>
            </div>
          </div>
        </div>

        <div class="card p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-red-100 rounded-md p-3">
              <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-500">截止日期</div>
              <div class="text-lg font-semibold text-gray-900">{{ formatDate(project.endDate) }}</div>
            </div>
          </div>
        </div>

        <div class="card p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-purple-100 rounded-md p-3">
              <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-500">负责人</div>
              <div class="text-lg font-semibold text-gray-900">{{ project.manager }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目内容标签页 -->
      <div class="card">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="[
                'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              {{ tab.name }}
            </button>
          </nav>
        </div>

        <!-- 基本信息 -->
        <div v-if="activeTab === 'info'" class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">项目基本信息</h3>
              <div class="space-y-4">
                <div>
                  <div class="text-sm font-medium text-gray-500">项目名称</div>
                  <div class="mt-1 text-gray-900">{{ project.name }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">项目编号</div>
                  <div class="mt-1 text-gray-900">{{ project.code }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">负责人</div>
                  <div class="mt-1 text-gray-900">{{ project.manager }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">项目状态</div>
                  <div class="mt-1">
                    <span :class="[`badge`, getStatusClass(project.status)]">{{ getStatusText(project.status) }}</span>
                  </div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">项目标签</div>
                  <div class="mt-1 flex flex-wrap gap-2">
                    <span v-for="(tag, index) in project.tags" :key="index" class="badge badge-gray">{{ tag }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">项目详细信息</h3>
              <div class="space-y-4">
                <div>
                  <div class="text-sm font-medium text-gray-500">设计方向</div>
                  <div class="mt-1 text-gray-900">{{ project.designDirection || '暂无' }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">施工方向</div>
                  <div class="mt-1 text-gray-900">{{ project.constructionDirection || '暂无' }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">监理单位</div>
                  <div class="mt-1 text-gray-900">{{ project.supervisorUnit || '暂无' }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">事项提醒</div>
                  <div class="mt-1 text-gray-900">{{ project.reminders || '暂无事项提醒' }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">项目描述</div>
                  <div class="mt-1 text-gray-900">{{ project.description || '暂无描述' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目进度 -->
        <div v-else-if="activeTab === 'progress'" class="p-6">
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">项目阶段</h3>
            <div class="relative">
              <div class="absolute inset-0 flex items-center" aria-hidden="true">
                <div class="w-full border-t border-gray-300"></div>
              </div>
              <div class="relative flex justify-between">
                <div v-for="(stage, index) in projectStages" :key="index" class="flex flex-col items-center">
                  <div
                    :class="[
                      'h-12 w-12 rounded-full flex items-center justify-center text-white text-sm font-medium',
                      index < currentStageIndex ? 'bg-blue-600' :
                      index === currentStageIndex ? 'bg-blue-500' : 'bg-gray-300'
                    ]"
                  >
                    {{ index + 1 }}
                  </div>
                  <div class="mt-2 text-sm font-medium text-gray-900">{{ stage.name }}</div>
                  <div class="mt-1 text-xs text-gray-500">{{ stage.date || '未开始' }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">完成情况</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="card p-4">
                <div class="text-sm font-medium text-gray-500 mb-1">总体进度</div>
                <div class="text-2xl font-bold text-gray-900 mb-2">{{ project.progress }}%</div>
                <div class="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                  <div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: `${project.progress}%` }"></div>
                </div>
              </div>

              <div class="card p-4">
                <div class="text-sm font-medium text-gray-500 mb-1">计划完成时间</div>
                <div class="text-2xl font-bold text-gray-900 mb-2">{{ formatDate(project.endDate) }}</div>
                <div class="text-sm text-gray-500">
                  {{ getRemainingDays(project.endDate) }}
                </div>
              </div>

              <div class="card p-4">
                <div class="text-sm font-medium text-gray-500 mb-1">延期情况</div>
                <div class="text-2xl font-bold" :class="project.isDelayed ? 'text-red-600' : 'text-green-600'">
                  {{ project.isDelayed ? '已延期' : '正常' }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ project.delayDays ? `延期 ${project.delayDays} 天` : '按计划进行中' }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目任务 -->
        <div v-else-if="activeTab === 'tasks'" class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-medium text-gray-900">任务列表</h3>
            <button class="btn btn-primary btn-sm">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              添加任务
            </button>
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务名称</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优先级</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">计划时间</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剩余工作量</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="task in projectTasks" :key="task.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ task.name }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="badge" :class="getTaskTypeClass(task.type)">{{ getTaskTypeText(task.type) }}</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="badge" :class="getTaskStatusClass(task.status)">{{ getTaskStatusText(task.status) }}</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="badge" :class="getTaskPriorityClass(task.priority)">{{ getTaskPriorityText(task.priority) }}</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ task.assignee }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(task.startDate) }} - {{ formatDate(task.endDate) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ task.remainingWork }} 小时
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                    <button class="text-red-600 hover:text-red-900">删除</button>
                  </td>
                </tr>
                <tr v-if="projectTasks.length === 0">
                  <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">
                    暂无任务数据
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 项目合同 -->
        <div v-else-if="activeTab === 'contract'" class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-medium text-gray-900">合同信息</h3>
            <button class="btn btn-primary btn-sm">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              添加合同
            </button>
          </div>

          <div class="grid grid-cols-1 gap-6">
            <div v-for="contract in projectContracts" :key="contract.id" class="card p-4">
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="text-lg font-medium text-gray-900">{{ contract.name }}</h4>
                  <p class="text-sm text-gray-500 mt-1">合同编号: {{ contract.code }}</p>
                </div>
                <span class="badge" :class="getContractStatusClass(contract.status)">{{ getContractStatusText(contract.status) }}</span>
              </div>

              <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <div class="text-sm font-medium text-gray-500">合同类型</div>
                  <div class="mt-1 text-sm text-gray-900">{{ contract.type }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">合同金额</div>
                  <div class="mt-1 text-sm text-gray-900">¥ {{ formatNumber(contract.amount) }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-500">签订日期</div>
                  <div class="mt-1 text-sm text-gray-900">{{ formatDate(contract.signDate) }}</div>
                </div>
              </div>

              <div class="mt-4">
                <div class="text-sm font-medium text-gray-500 mb-2">付款信息</div>
                <div class="bg-gray-50 p-3 rounded-md">
                  <div v-for="(payment, index) in contract.payments" :key="index" class="flex justify-between py-1 border-b border-gray-200 last:border-0">
                    <span class="text-sm text-gray-700">{{ payment.name }}</span>
                    <div class="text-right">
                      <span class="text-sm font-medium text-gray-900">¥ {{ formatNumber(payment.amount) }}</span>
                      <span class="text-xs text-gray-500 ml-2">{{ payment.status === 'paid' ? '已付款' : '未付款' }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-4 flex justify-between items-center">
                <div class="flex space-x-2">
                  <button class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                    </svg>
                    附件 ({{ contract.attachments.length }})
                  </button>
                </div>
                <div class="flex space-x-2">
                  <button class="text-sm text-blue-600 hover:text-blue-800">查看</button>
                  <button class="text-sm text-red-600 hover:text-red-800">删除</button>
                </div>
              </div>
            </div>

            <div v-if="projectContracts.length === 0" class="text-center py-8 text-gray-500">
              暂无合同信息
            </div>
          </div>
        </div>

        <!-- 项目采购 -->
        <div v-else-if="activeTab === 'purchases'" class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-medium text-gray-900">采购信息</h3>
            <button class="btn btn-primary btn-sm">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              添加采购
            </button>
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">采购内容</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发票类型</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">采购时间</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="purchase in projectPurchases" :key="purchase.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ purchase.content }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ purchase.supplier }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ purchase.quantity }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ¥ {{ formatNumber(purchase.amount) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ purchase.invoiceType }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(purchase.purchaseDate) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                    <button class="text-red-600 hover:text-red-900">删除</button>
                  </td>
                </tr>
                <tr v-if="projectPurchases.length === 0">
                  <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                    暂无采购数据
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除错误提示 -->
  <div v-if="deleteError" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <div class="text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
          <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-1">删除失败</h3>
        <p class="text-gray-500 whitespace-pre-line">{{ deleteError }}</p>
        <div class="mt-6 flex justify-center">
          <button
            type="button"
            @click="deleteError = null"
            class="btn btn-primary"
          >
            确定
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认弹窗 -->
  <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <div class="text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
          <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-1">确认删除项目</h3>
        <p class="text-gray-500">
          尊敬的用户，您确定要删除项目 "{{ project?.name }}" 吗？
          <br><br>
          <span class="text-red-600 font-medium">请您注意以下重要影响：</span>
          <br>
          · 此操作执行后将<span class="font-medium">无法恢复</span><br>
          · 所有项目相关数据将被<span class="font-medium">永久删除</span><br>
          · 包括项目文档、任务记录和团队分配信息<br>
          · 相关的工时记录和统计数据可能会受到影响<br>
        </p>

        <div class="mt-6 flex justify-center space-x-3">
          <button
            type="button"
            @click="showDeleteModal = false"
            class="btn btn-secondary"
          >
            取消
          </button>
          <button
            type="button"
            @click="deleteProject"
            class="btn btn-danger"
            :disabled="isDeleting"
          >
            <span v-if="isDeleting">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              删除中...
            </span>
            <span v-else>确认删除</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';

export default {
  setup() {
    const route = useRoute();
    const router = useRouter();
    const loading = ref(true);
    const error = ref(null);
    const project = ref(null);
    const activeTab = ref('info');
    const showDeleteModal = ref(false);
    const isDeleting = ref(false);
    const deleteError = ref(null);

    // 项目阶段
    const projectStages = ref([]);
    const currentStageIndex = ref(0);

    // 项目任务
    const projectTasks = ref([]);

    // 项目合同
    const projectContracts = ref([]);

    // 项目采购
    const projectPurchases = ref([]);

    // 标签页定义
    const tabs = [
      { id: 'info', name: '基本信息' },
      { id: 'progress', name: '项目进度' },
      { id: 'tasks', name: '项目任务' },
      { id: 'contract', name: '项目合同' },
      { id: 'purchases', name: '项目采购' }
    ];

    // 获取项目详情
    const fetchProjectDetail = async () => {
      loading.value = true;
      error.value = null;

      try {
        const projectId = route.params.id;
        const response = await axios.get(`/api/projects/${projectId}`);
        project.value = response.data;

        // 获取项目阶段
        const stagesResponse = await axios.get(`/api/projects/${projectId}/stages`);
        projectStages.value = stagesResponse.data || [];

        // 计算当前阶段
        currentStageIndex.value = projectStages.value.findIndex(stage => stage.status === 'current') || 0;

        // 获取项目任务
        const tasksResponse = await axios.get(`/api/projects/${projectId}/tasks`);
        projectTasks.value = tasksResponse.data || [];

        // 获取项目合同
        const contractsResponse = await axios.get(`/api/projects/${projectId}/contracts`);
        projectContracts.value = contractsResponse.data || [];

        // 获取项目采购
        const purchasesResponse = await axios.get(`/api/projects/${projectId}/purchases`);
        projectPurchases.value = purchasesResponse.data || [];
      } catch (err) {
        console.error('获取项目详情失败:', err);
        error.value = err.response?.data?.message || '获取项目详情失败，请重试';
        project.value = null;
      } finally {
        loading.value = false;
      }
    };

    // 确认删除项目
    const confirmDelete = () => {
      showDeleteModal.value = true;
    };

    // 删除项目
    const deleteProject = async () => {
      isDeleting.value = true;

      try {
        await axios.delete(`/api/projects/${project.value.id}`);
        router.push('/projects');
      } catch (err) {
        console.error('删除项目失败:', err);
        deleteError.value = '很抱歉，删除项目时遇到了问题。\n\n可能的原因：\n· 该项目可能正在被其他用户编辑\n· 项目中存在关联的任务或合同数据\n· 网络连接暂时中断\n· 系统临时故障\n· 您可能没有足够的权限执行此操作\n\n建议您：\n1. 检查网络连接\n2. 确认项目中没有未完成的任务\n3. 确认所有合同和采购记录已处理\n4. 稍后再次尝试\n\n如果问题持续存在，请联系系统管理员获取帮助。';
        showDeleteModal.value = false;
      } finally {
        isDeleting.value = false;
      }
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未设置';

      try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      } catch (err) {
        console.error('日期格式化错误:', err);
        return dateString;
      }
    };

    // 格式化数字
    const formatNumber = (num) => {
      if (num === undefined || num === null) return '0';
      return num.toLocaleString('zh-CN');
    };

    // 计算剩余天数
    const getRemainingDays = (endDateString) => {
      if (!endDateString) return '未设置截止日期';

      try {
        const endDate = new Date(endDateString);
        const today = new Date();

        // 重置时间部分以便准确计算天数差异
        endDate.setHours(0, 0, 0, 0);
        today.setHours(0, 0, 0, 0);

        const diffTime = endDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) {
          return `已超期 ${Math.abs(diffDays)} 天`;
        } else if (diffDays === 0) {
          return '今天截止';
        } else {
          return `剩余 ${diffDays} 天`;
        }
      } catch (err) {
        console.error('计算剩余天数错误:', err);
        return '日期格式错误';
      }
    };

    // 获取状态样式类
    const getStatusClass = (status) => {
      switch (status) {
        case 'planning': return 'badge-blue';
        case 'in_progress': return 'badge-green';
        case 'on_hold': return 'badge-yellow';
        case 'completed': return 'badge-purple';
        case 'cancelled': return 'badge-red';
        default: return 'badge-gray';
      }
    };

    // 获取状态文本
    const getStatusText = (status) => {
      switch (status) {
        case 'planning': return '规划中';
        case 'in_progress': return '进行中';
        case 'on_hold': return '已暂停';
        case 'completed': return '已完成';
        case 'cancelled': return '已取消';
        default: return '未知状态';
      }
    };

    // 获取任务类型样式类
    const getTaskTypeClass = (type) => {
      switch (type) {
        case 'design': return 'badge-blue';
        case 'development': return 'badge-green';
        case 'testing': return 'badge-yellow';
        case 'deployment': return 'badge-purple';
        default: return 'badge-gray';
      }
    };

    // 获取任务类型文本
    const getTaskTypeText = (type) => {
      switch (type) {
        case 'design': return '设计';
        case 'development': return '开发';
        case 'testing': return '测试';
        case 'deployment': return '部署';
        default: return '其他';
      }
    };

    // 获取任务状态样式类
    const getTaskStatusClass = (status) => {
      switch (status) {
        case 'todo': return 'badge-gray';
        case 'in_progress': return 'badge-blue';
        case 'review': return 'badge-yellow';
        case 'done': return 'badge-green';
        default: return 'badge-gray';
      }
    };

    // 获取任务状态文本
    const getTaskStatusText = (status) => {
      switch (status) {
        case 'todo': return '待处理';
        case 'in_progress': return '进行中';
        case 'review': return '审核中';
        case 'done': return '已完成';
        default: return '未知状态';
      }
    };

    // 获取任务优先级样式类
    const getTaskPriorityClass = (priority) => {
      switch (priority) {
        case 'low': return 'badge-gray';
        case 'medium': return 'badge-blue';
        case 'high': return 'badge-yellow';
        case 'urgent': return 'badge-red';
        default: return 'badge-gray';
      }
    };

    // 获取任务优先级文本
    const getTaskPriorityText = (priority) => {
      switch (priority) {
        case 'low': return '低';
        case 'medium': return '中';
        case 'high': return '高';
        case 'urgent': return '紧急';
        default: return '未设置';
      }
    };

    // 获取合同状态样式类
    const getContractStatusClass = (status) => {
      switch (status) {
        case 'draft': return 'badge-gray';
        case 'pending': return 'badge-yellow';
        case 'active': return 'badge-green';
        case 'completed': return 'badge-blue';
        case 'terminated': return 'badge-red';
        default: return 'badge-gray';
      }
    };

    // 获取合同状态文本
    const getContractStatusText = (status) => {
      switch (status) {
        case 'draft': return '草稿';
        case 'pending': return '待签署';
        case 'active': return '生效中';
        case 'completed': return '已完成';
        case 'terminated': return '已终止';
        default: return '未知状态';
      }
    };

    onMounted(() => {
      fetchProjectDetail();
    });

    return {
      loading,
      error,
      project,
      activeTab,
      tabs,
      projectStages,
      currentStageIndex,
      projectTasks,
      projectContracts,
      projectPurchases,
      showDeleteModal,
      isDeleting,
      deleteError,
      formatDate,
      formatNumber,
      getRemainingDays,
      getStatusClass,
      getStatusText,
      getTaskTypeClass,
      getTaskTypeText,
      getTaskStatusClass,
      getTaskStatusText,
      getTaskPriorityClass,
      getTaskPriorityText,
      getContractStatusClass,
      getContractStatusText,
      confirmDelete,
      deleteProject
    };
  }
};
</script>