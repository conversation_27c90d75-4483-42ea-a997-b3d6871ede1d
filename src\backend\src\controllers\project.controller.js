const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { projectService } = require('../services');
const pick = require('../utils/pick');

/**
 * Create a new project
 * @route POST /api/projects
 */
const createProject = catchAsync(async (req, res) => {
  const projectData = { ...req.body, createdBy: req.user.id };
  const project = await projectService.createProject(projectData);
  res.status(201).send(project);
});

/**
 * Get all projects with filtering
 * @route GET /api/projects
 */
const getProjects = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['name', 'status', 'usageType', 'engineeringType', 'startDate', 'endDate', 'tag', 'search', 'manager']);
  const options = pick(req.query, ['sortBy', 'sortOrder', 'page', 'limit']);

  // Parse numeric values
  if (options.page) options.page = parseInt(options.page, 10);
  if (options.limit) options.limit = parseInt(options.limit, 10);

  // Parse dates
  if (filter.startDate) filter.startDate = new Date(filter.startDate);
  if (filter.endDate) filter.endDate = new Date(filter.endDate);

  // Filter out empty strings (they should be treated as undefined)
  Object.keys(filter).forEach(key => {
    if (filter[key] === '') {
      delete filter[key];
    }
  });

  // Parse tags if they exist
  if (filter.tags) {
    filter.tags = Array.isArray(filter.tags) ? filter.tags : [filter.tags];
  }

  const result = await projectService.queryProjects(filter, options);
  res.send(result);
});

/**
 * Get a project by ID
 * @route GET /api/projects/:projectId
 */
const getProject = catchAsync(async (req, res) => {
  const project = await projectService.getProjectById(req.params.projectId);
  res.send(project);
});

/**
 * Update a project
 * @route PATCH /api/projects/:projectId
 */
const updateProject = catchAsync(async (req, res) => {
  const project = await projectService.updateProjectById(req.params.projectId, req.body);
  res.send(project);
});

/**
 * Delete a project
 * @route DELETE /api/projects/:projectId
 */
const deleteProject = catchAsync(async (req, res) => {
  await projectService.deleteProjectById(req.params.projectId);
  // 使用明确的数字状态码，避免可能的undefined问题
  res.status(204).send();
});

/**
 * 获取所有去重的建设单位
 * @route GET /api/projects/construction-units
 */
const getConstructionUnits = catchAsync(async (req, res) => {
  const units = await projectService.getConstructionUnits(req.query.search);
  res.send(units);
});

/**
 * 获取项目列表用于下拉选择
 * @route GET /api/projects/dropdown
 */
const getProjectsForDropdown = catchAsync(async (req, res) => {
  const projects = await projectService.getProjectsForDropdown(req.query.search);
  res.send(projects);
});

module.exports = {
  createProject,
  getProjects,
  getProject,
  updateProject,
  deleteProject,
  getConstructionUnits,
  getProjectsForDropdown
};