<template>
  <div class="max-w-7xl mx-auto px-4 py-6">
    <!-- 顶部操作栏 -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center space-x-4">
        <h1 class="text-2xl font-bold text-gray-900">{{ isPublicMode ? '项目展示' : '项目管理' }}</h1>
        <div class="flex items-center space-x-2">
          <button
            @click="viewMode = 'card'"
            class="p-2 rounded-lg hover:bg-gray-100"
            :class="{ 'bg-gray-100': viewMode === 'card' }"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
          </button>
          <button
            @click="viewMode = 'table'"
            class="p-2 rounded-lg hover:bg-gray-100"
            :class="{ 'bg-gray-100': viewMode === 'table' }"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </button>
          <router-link v-if="!isPublicMode" to="/projects/stats"
            class="px-4 py-2 rounded-lg text-sm font-medium bg-purple-100 text-purple-700 hover:bg-purple-200 transition-colors duration-200 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            项目统计
          </router-link>
          <router-link v-if="isPublicMode" to="/login"
            class="px-4 py-2 rounded-lg text-sm font-medium bg-blue-100 text-blue-700 hover:bg-blue-200 transition-colors duration-200 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
            </svg>
            登录系统
          </router-link>
        </div>
      </div>
      <router-link v-if="!isPublicMode" to="/projects/create" class="btn btn-primary">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        新建项目 2
      </router-link>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-card mb-6">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="project-search" class="form-label font-medium">搜索项目</label>
          <div class="relative">
            <input
              id="project-search"
              v-model="searchQuery"
              type="text"
              placeholder="搜索项目名称..."
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              @input="debouncedFetchProjects"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 flex gap-4">
          <div class="flex-1">
            <label for="status-filter" class="form-label font-medium">项目状态</label>
            <select id="status-filter" v-model="statusFilter" class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200">
              <option value="">所有状态</option>
              <option value="planning">规划中</option>
              <option value="in_progress">进行中</option>
              <option value="completed">已完成</option>
              <option value="on_hold">已暂停</option>
              <option value="cancelled">已取消</option>
            </select>
          </div>
          <div class="flex-1">
            <label for="manager-filter" class="form-label font-medium">负责人</label>
            <select id="manager-filter" v-model="managerFilter" class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200">
              <option value="">所有负责人</option>
              <option v-for="manager in managers" :key="manager" :value="manager">{{ manager }}</option>
            </select>
          </div>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button @click="resetFilters" class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200" :disabled="loading">重置</button>
        <button @click="fetchProjects" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200" :disabled="loading">
          <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
          {{ loading ? '加载中...' : '查询' }}
        </button>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>

    <!-- 项目列表 -->
    <div v-else>
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="project in filteredProjects" :key="project.id" class="card hover:shadow-lg transition-shadow duration-200">
          <div class="p-6">
            <div class="flex justify-between items-start mb-4">
              <h3 class="text-lg font-semibold text-gray-900">{{ project.name }}</h3>
              <span :class="['badge', getStatusClass(project.status)]">{{ getStatusText(project.status) }}</span>
            </div>
            <div class="space-y-2 text-sm text-gray-600">
              <p>项目编号：{{ project.code }}</p>
              <p>负责人：{{ project.manager }}</p>
              <p>开始日期：{{ formatDate(project.startDate) }}</p>
              <p>结束日期：{{ formatDate(project.endDate) }}</p>
            </div>
            <div class="mt-4">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm text-gray-600">项目进度</span>
                <span class="text-sm font-medium" :class="getProgressTextColor(project.progress)">{{ project.progress }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="h-2 rounded-full"
                  :class="getProgressBarColor(project.progress)"
                  :style="{ width: `${project.progress}%` }"
                ></div>
              </div>
            </div>            <div v-if="!isPublicMode" class="mt-6 flex justify-end space-x-3">
              <router-link
                :to="`/projects/edit/${project.id}`"
                class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
              >
                <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                </svg>
                <span class="font-medium">编辑</span>
              </router-link>
              <button
                @click="handleDelete(project.id)"
                class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
              >
                <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                </svg>
                <span class="font-medium">删除</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-else class="card overflow-hidden shadow-md">
        <div class="overflow-x-auto relative">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <!-- 项目编号 -->
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目成员</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目负责人</th>

                <!-- 关联单位 -->
                <th colspan="5" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">关联单位</th>

                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目地址</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目时间</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目档案号</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工程编号</th>

                <!-- 使用性质和工程性质 -->
                <th colspan="2" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">项目性质</th>

                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">施工许可证号</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">面积</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">特殊系统</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">公司业务负责人</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">甲方项目对接人</th>
                <th rowspan="2" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky right-0 bg-gray-50 z-10">操作</th>
              </tr>
              <tr>
                <!-- 关联单位二级表头 -->
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">建设单位</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设计单位</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">施工单位</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">监理单位</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审图单位</th>

                <!-- 项目性质二级表头 -->
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用性质</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工程性质</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="project in filteredProjects" :key="project.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.code }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">
                    <template v-if="Array.isArray(project.members) && project.members.length > 0">
                      {{ getMemberNames(project.members) }}
                    </template>
                    <template v-else>
                      -
                    </template>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.manager }}</div>
                </td>

                <!-- 关联单位 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.constructionUnit }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.designUnit }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.contractorUnit }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.supervisorUnit }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.reviewUnit }}</div>
                </td>

                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.address }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ formatDate(project.startDate) }} - {{ formatDate(project.endDate) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.archiveNumber }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.engineeringNumber }}</div>
                </td>

                <!-- 项目性质 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ usageTypeMap[project.usageType] || project.usageType || '-' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ engineeringTypeMap[project.engineeringType] || project.engineeringType || '-' }}</div>
                </td>

                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.constructionPermitNumber }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.area }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.specialSystem }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.companyManager }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="['badge', getStatusClass(project.status)]">{{ getStatusText(project.status) }}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ project.clientContact }}</div>
                </td>
                <td v-if="!isPublicMode" class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium sticky right-0 bg-white z-10">                  <router-link
                    :to="`/projects/edit/${project.id}`"
                    class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                    </svg>
                    <span class="font-medium">编辑</span>
                  </router-link>
                  <button
                    @click="handleDelete(project.id)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <span class="font-medium">删除</span>
                  </button>
                </td>
                <td v-else class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium sticky right-0 bg-white z-10">
                  <span class="text-gray-400">公开查看</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 分页 -->
      <div class="mt-8 flex justify-between items-center px-2">
        <div class="text-sm text-gray-500">
          显示 {{ startIndex + 1 }} 到 {{ Math.min(endIndex, totalProjects) }} 共 {{ totalProjects }} 个项目
          <span class="ml-2 text-xs text-gray-400">最后更新时间: {{ lastUpdated }}</span>
        </div>

        <!-- 分页控件 -->
        <div v-if="totalPages > 1" class="flex items-center space-x-2">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-1 rounded border flex items-center"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
            :style="{ borderColor: 'var(--border-color)' }"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>

          <template v-for="page in displayedPages" :key="page">
            <span
              v-if="page === '...'"
              class="px-3 py-1"
            >
              ...
            </span>
            <button
              v-else
              @click="changePage(page)"
              class="px-3 py-1 rounded border"
              :class="{ 'font-bold': currentPage === page }"
              :style="{
                backgroundColor: currentPage === page ? 'var(--primary-color)' : 'transparent',
                color: currentPage === page ? 'white' : 'var(--text-primary)',
                borderColor: 'var(--border-color)'
              }"
            >
              {{ page }}
            </button>
          </template>

          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-1 rounded border flex items-center"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
            :style="{ borderColor: 'var(--border-color)' }"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive, onUnmounted } from 'vue';
import apiService from '@/services/apiService';
import { IS_PROD } from '@/utils/env';
import { useRoute } from 'vue-router';

// 路由信息
const route = useRoute();
const isPublicMode = computed(() => route.path.includes('/public'));

// 基本状态
const projects = ref([]);
const loading = ref(false);
const error = ref(null);
const successMessage = ref(null);
const lastUpdated = ref(new Date().toLocaleString());
let refreshInterval = null;

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalProjects = ref(0);
const totalPages = ref(1);

// 搜索和过滤
const searchQuery = ref('');
const statusFilter = ref('');
const managerFilter = ref('');
const viewMode = ref(localStorage.getItem('projectViewMode') || 'card');
const managers = ref([]);
const userOptions = ref([]);

// 当前数据源类型
const isMockData = computed(() => {
  // 检查全局数据源变量
  if (window.__currentDataSource) {
    return window.__currentDataSource === 'mock';
  }

  // 从 localStorage 获取
  const storedDataSource = localStorage.getItem('app_data_source');
  if (storedDataSource) {
    return storedDataSource === 'mock';
  }

  // 默认使用真实API
  return false;
});

// 监听视图模式变化并保存到 localStorage
watch(viewMode, (newMode) => {
  localStorage.setItem('projectViewMode', newMode);
});

// 计算属性
const startIndex = computed(() => (currentPage.value - 1) * pageSize.value);
const endIndex = computed(() => startIndex.value + pageSize.value);

// 过滤后的项目列表
const filteredProjects = computed(() => {
  if (!projects.value.length) return [];

  return projects.value.filter(project => {
    // 搜索条件
    const matchesSearch = !searchQuery.value ||
      (project.name && project.name.toLowerCase().includes(searchQuery.value.toLowerCase())) ||
      (project.code && project.code.toLowerCase().includes(searchQuery.value.toLowerCase()));

    // 状态过滤
    const matchesStatus = !statusFilter.value || project.status === statusFilter.value;

    // 负责人过滤
    const matchesManager = !managerFilter.value || project.manager === managerFilter.value;

    return matchesSearch && matchesStatus && matchesManager;
  });
});

// 计算要显示的页码
const displayedPages = computed(() => {
  if (totalPages.value <= 7) {
    // 如果总页数小于等于7，显示所有页码
    return Array.from({ length: totalPages.value }, (_, i) => i + 1);
  }

  // 否则显示带省略号的页码
  const pages = [];

  // 始终显示第一页
  pages.push(1);

  // 当前页靠近开始
  if (currentPage.value <= 3) {
    pages.push(2, 3, 4, '...', totalPages.value - 1, totalPages.value);
  }
  // 当前页靠近结尾
  else if (currentPage.value >= totalPages.value - 2) {
    pages.push('...', totalPages.value - 3, totalPages.value - 2, totalPages.value - 1, totalPages.value);
  }
  // 当前页在中间
  else {
    pages.push(
      '...',
      currentPage.value - 1,
      currentPage.value,
      currentPage.value + 1,
      '...',
      totalPages.value
    );
  }

  return pages;
});

// 状态相关函数
const statusMap = reactive({
  'planning': '规划中',
  'in_progress': '进行中',
  'completed': '已完成',
  'on_hold': '已暂停',
  'cancelled': '已取消'
});

const getStatusText = (status) => statusMap[status] || status;

const getStatusClass = (status) => {
  if (!status) return 'badge-gray';
  const statusClasses = {
    'planning': 'badge-blue',
    'in_progress': 'badge-green',
    'completed': 'badge-purple',
    'on_hold': 'badge-yellow',
    'cancelled': 'badge-red'
  };
  return statusClasses[status] || 'badge-gray';
};

// 进度相关函数
const getProgressBarColor = (progress) => {
  if (!progress && progress !== 0) return 'bg-gray-300';
  if (progress < 25) return 'bg-red-500';
  if (progress < 50) return 'bg-yellow-500';
  if (progress < 75) return 'bg-blue-500';
  return 'bg-green-500';
};

const getProgressTextColor = (progress) => {
  if (!progress && progress !== 0) return 'text-gray-500';
  if (progress < 25) return 'text-red-600';
  if (progress < 50) return 'text-yellow-600';
  if (progress < 75) return 'text-blue-600';
  return 'text-green-600';
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '-';

  return date.toLocaleDateString('zh-CN');
};

// 获取项目列表
const fetchProjects = async () => {
  loading.value = true;
  error.value = null;

  try {
    let api = isPublicMode.value ? apiService.getPublicProjects : apiService.getProjects;
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
    };
    
    // 添加搜索和过滤条件
    if (searchQuery.value) params.search = searchQuery.value;
    if (statusFilter.value) params.status = statusFilter.value;
    if (managerFilter.value) params.manager = managerFilter.value;
    
    console.log('Fetching projects with params:', params);
    const response = await api(params);
    console.log('API response:', response);
    
    // 正确处理不同格式的响应
    if (response && response.data) {
      // 如果响应直接包含数据
      projects.value = response.data;
      console.log('Projects data loaded (from response.data):', projects.value);
      
      // 处理分页信息
      if (response.totalCount !== undefined) {
        totalProjects.value = response.totalCount;
        totalPages.value = Math.ceil(response.totalCount / pageSize.value);
      } else {
        totalProjects.value = projects.value.length;
        totalPages.value = 1;
      }
    } else if (response && response.results) {
      // 如果响应包含 results 字段
      projects.value = response.results;
      console.log('Projects data loaded (from response.results):', projects.value);
      
      // 处理分页信息
      if (response.totalResults !== undefined) {
        totalProjects.value = response.totalResults;
        totalPages.value = response.totalPages || Math.ceil(response.totalResults / pageSize.value);
      } else {
        totalProjects.value = projects.value.length;
        totalPages.value = 1;
      }
    } else {
      console.error('Invalid response format:', response);
      projects.value = [];
      error.value = '服务器返回的数据格式不正确';
      return;
    }
    
    // 收集所有管理者进行筛选
    const uniqueManagers = new Set();
    projects.value.forEach(project => {
      if (project && project.manager) {
        uniqueManagers.add(project.manager);
      }
    });
    managers.value = Array.from(uniqueManagers).sort();
    
    lastUpdated.value = new Date().toLocaleString();
  } catch (err) {
    console.error('获取项目列表失败:', err);
    projects.value = [];
    error.value = '获取项目列表失败：' + (err.message || '未知错误');
  } finally {
    loading.value = false;
  }
};

// 切换页面
const changePage = (page) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) {
    return;
  }

  currentPage.value = page;
  fetchProjects();
};

// 处理删除
const handleDelete = async (projectId) => {
  // 获取项目名称
  const project = projects.value.find(p => p.id === projectId);
  const projectName = project ? project.name : '此项目';

  if (!window.confirm(`尊敬的用户，您确定要删除"${projectName}"吗？\n\n请您注意以下重要影响：\n· 此操作执行后将无法恢复\n· 所有项目相关数据将被永久删除\n· 包括项目文档、任务记录和团队分配信息\n· 相关的工时记录和统计数据可能会受到影响\n\n如果您确定要删除此项目，请点击"确定"按钮继续操作。`)) {
    return;
  }

  try {
    loading.value = true;
    await apiService.deleteProject(projectId);
    showSuccessMessage(`项目"${projectName}"已成功删除。\n\n系统已移除所有相关数据，包括项目文档、任务记录和团队分配信息。`);
    await fetchProjects();
  } catch (err) {
    error.value = '很抱歉，删除项目时遇到了问题。\n\n可能的原因：\n· 网络连接暂时中断\n· 系统临时故障\n· 该项目可能已被关联到其他重要数据\n· 您可能没有足够的权限执行此操作\n\n建议您：\n1. 检查网络连接\n2. 确认您有删除权限\n3. 稍后再次尝试\n\n如果问题持续存在，请联系系统管理员获取帮助。';
    console.error('删除项目失败:', err);
  } finally {
    loading.value = false;
  }
};

// 设置自动刷新
const setupRefresh = () => {
  // 清除已有的定时器
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }

  // 生产环境且使用API时，每60秒自动刷新一次
  if (IS_PROD && !isMockData.value) {
    refreshInterval = setInterval(() => {
      fetchProjects();
    }, 60000); // 60秒
  }
};

// 监听搜索和过滤条件变化
watch([searchQuery, statusFilter, managerFilter], () => {
  // 重置到第一页
  currentPage.value = 1;
  // 重新加载数据
  fetchProjects();
}, { debounce: 300 }); // 添加防抖动以避免频繁请求

// 监听数据源变化
watch(isMockData, () => {
  // 重置到第一页
  currentPage.value = 1;
  // 重新加载数据
  fetchProjects();
  // 重新设置自动刷新
  setupRefresh();
});

// 页面加载时获取项目数据并设置自动刷新
onMounted(async () => {
  // 获取用户列表
  try {
    console.log('Fetching user list...');
    const users = await apiService.users.getUsers();
    console.log('Users loaded:', users);
    userOptions.value = users || [];
  } catch (err) {
    console.error('获取用户列表失败:', err);
    userOptions.value = []; // Initialize to empty array on error
  }

  // 然后加载项目列表
  fetchProjects();
  setupRefresh();

  // 添加数据源变化事件监听
  window.addEventListener('datasource-changed', () => {
    fetchProjects();
    setupRefresh();
  });
});

// 页面卸载时清除定时器和事件监听器
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
});

// 在<script setup>顶部添加映射
const usageTypeMap = reactive({
  'commercial': '商业',
  'residential': '住宅',
  'industrial': '工业',
  'public': '公共建筑'
});

const engineeringTypeMap = reactive({
  'building': '建筑工程',
  'fire': '消防工程',
  'annual': '年度检测',
  'completion': '竣工检测',
  'safety': '消防安全评估',
  'maintenance': '消防维保'
});

// 获取成员姓名
function getMemberNames(memberIds) {
  // Safety check for invalid input
  if (!memberIds || !Array.isArray(memberIds) || memberIds.length === 0) {
    return '-';
  }

  // Check if userOptions is loaded
  if (!userOptions.value || userOptions.value.length === 0) {
    console.log('User options not loaded yet, displaying IDs');
    return memberIds.join('、');
  }

  try {
    return memberIds.map(id => {
      // Safety check for invalid ID
      if (!id) return '-';
      
      const user = userOptions.value.find(user => user.id === id);
      return user ? user.name : id; // Fall back to ID if user not found
    }).filter(Boolean).join('、') || '-';
  } catch (err) {
    console.error('Error mapping member names:', err);
    return memberIds.join('、'); // Fall back to IDs on error
  }
}
</script>

<style scoped>
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-blue {
  @apply bg-blue-100 text-blue-800;
}

.badge-green {
  @apply bg-green-100 text-green-800;
}

.badge-purple {
  @apply bg-purple-100 text-purple-800;
}

.badge-yellow {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-red {
  @apply bg-red-100 text-red-800;
}

.badge-gray {
  @apply bg-gray-100 text-gray-800;
}

/* 表格样式 */
table {
  border-collapse: separate;
  border-spacing: 0;
}

th {
  background-color: #f9fafb;
  border-bottom: 2px solid #e5e7eb;
}

td {
  border-bottom: 1px solid #e5e7eb;
}

tr:hover td {
  background-color: #f9fafb;
}

/* 固定列样式 */
.sticky {
  position: sticky;
}

.right-0 {
  right: 0;
}

.z-10 {
  z-index: 10;
}

/* 确保固定列有阴影效果 */
.sticky.right-0::after {
  content: '';
  position: absolute;
  top: 0;
  left: -6px;
  height: 100%;
  width: 6px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.05), transparent);
}
</style>