<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <h1 class="page-title">{{ isEditMode ? '编辑采购单' : '新建采购单' }}</h1>
      </div>
      <div class="flex space-x-4">
        <router-link to="/purchases" class="btn btn-secondary flex items-center text-sm">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
                    返回列表
        </router-link>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading && !error" class="card shadow-md p-8 text-center">
      <div class="flex justify-center">
        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
      <p class="mt-4 text-gray-600">正在加载数据...</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
    </div>
    
    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 表单区域 -->
    <div v-if="!isLoading || error" class="card shadow-md">
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- 基本信息 -->
        <div class="form-section">
          <h2 class="form-section-title">基本信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="purchase-number" class="form-label">采购单号</label>
              <input
                type="text"
                id="purchase-number"
                v-model="purchaseForm.number"
                class="form-input"
                placeholder="系统自动生成"
                disabled
              />
            </div>
            <div>
              <label for="purchase-date" class="form-label">采购日期 <span class="form-required">*</span></label>
              <input
                type="date"
                id="purchase-date"
                v-model="purchaseForm.date"
                class="form-input"
                required
                max="3000-12-31"
              />
            </div>
            <div>
              <label for="supplier" class="form-label">供应商 <span class="form-required">*</span></label>
              <select id="supplier" v-model="purchaseForm.supplierId" class="form-input" required>
                <option value="" disabled>-- 选择供应商 --</option>
                <option v-for="supplier in suppliers" :key="supplier.id" :value="supplier.id">
                  {{ supplier.name }}
                </option>
              </select>
            </div>
            <div>
              <label for="status" class="form-label">状态</label>
              <select id="status" v-model="purchaseForm.status" class="form-input">
                <option value="draft">草稿</option>
                <option value="pending">待审核</option>
                <option value="approved">已审核</option>
              </select>
            </div>
            <div>
              <label for="project" class="form-label">关联项目</label>
              <select id="project" v-model="purchaseForm.projectId" class="form-input">
                <option value="">-- 选择项目（可选）--</option>
                <option v-for="project in projects" :key="project.id" :value="project.id">
                  {{ project.name }}
                </option>
              </select>
            </div>
            <div>
              <label for="recipient" class="form-label">收货人</label>
              <input
                type="text"
                id="recipient"
                v-model="purchaseForm.recipient"
                class="form-input"
                placeholder="请输入收货人姓名"
              />
            </div>
            <div>
              <label for="expected-delivery-date" class="form-label">预计到货时间</label>
              <input
                type="date"
                id="expected-delivery-date"
                v-model="purchaseForm.expectedDeliveryDate"
                class="form-input"
                max="3000-12-31"
              />
            </div>
            <div>
              <label for="delivery-address" class="form-label">收货地址</label>
              <input
                type="text"
                id="delivery-address"
                v-model="purchaseForm.deliveryAddress"
                class="form-input"
                placeholder="请输入收货地址"
              />
            </div>
          </div>
        </div>

        <!-- 采购项目 -->
        <div class="form-section">
          <h2 class="form-section-title">采购项目</h2>

          <!-- 采购项目表格 -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物品</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">不含税单价</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">税率</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">含税单价</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">税额</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">含税金额</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="(item, index) in purchaseForm.items" :key="index" class="hover:bg-gray-50">
                  <td class="px-4 py-4 whitespace-nowrap">
                    <select v-model="item.productId" class="form-input py-1" required @change="handleProductChange(index)">
                      <option value="" disabled>-- 选择物品 --</option>
                      <option v-for="product in products" :key="product.id" :value="product.id">
                        {{ product.name }}
                      </option>
                    </select>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <input type="text" v-model="item.specification" class="form-input py-1" placeholder="规格" />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <input
                      type="number"
                      v-model.number="item.quantity"
                      @input="calculateItemAmount(index)"
                      class="form-input py-1 w-24"
                      min="1"
                      required
                    />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <input
                      type="number"
                      v-model.number="item.priceExcludingTax"
                      @input="calculateItemAmount(index)"
                      class="form-input py-1 w-32"
                      min="0"
                      step="0.01"
                      placeholder="不含税单价"
                      required
                    />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <select
                      v-model.number="item.taxRate"
                      @change="calculateItemAmount(index)"
                      class="form-input py-1 w-24"
                      required
                    >
                      <option value="">税率</option>
                      <option value="0.13">13%</option>
                      <option value="0.09">9%</option>
                      <option value="0.06">6%</option>
                      <option value="0.03">3%</option>
                      <option value="0">0%</option>
                    </select>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <span class="text-gray-700">¥{{ formatCurrency(item.priceIncludingTax) }}</span>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <span class="text-gray-700">¥{{ formatCurrency(item.taxAmount) }}</span>
                  </td>                  <td class="px-4 py-4 whitespace-nowrap">
                    <span class="text-gray-700">¥{{ formatCurrency(item.amountIncludingTax) }}</span>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <button
                      type="button"
                      @click="removeItem(index)"
                      class="text-red-600 hover:text-red-900"
                      title="删除项目"
                    >
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 添加项目按钮 -->
          <div class="mt-4">
            <button
              type="button"
              @click="addItem"
              class="btn btn-secondary flex items-center text-sm"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              添加项目
            </button>
          </div>

          <!-- 总计 -->
          <div class="mt-6 text-right">
            <div class="inline-block bg-gray-50 rounded-lg p-4 space-y-2">
              <div class="text-gray-600">不含税总额: <span class="text-lg font-semibold text-gray-800 ml-2">¥{{ formatCurrency(totalAmountExcludingTax) }}</span></div>
              <div class="text-gray-600">税额合计: <span class="text-lg font-semibold text-gray-800 ml-2">¥{{ formatCurrency(totalTaxAmount) }}</span></div>
              <div class="text-gray-600 border-t pt-2">含税总额: <span class="text-xl font-bold text-gray-800 ml-2">¥{{ formatCurrency(totalAmountIncludingTax) }}</span></div>
            </div>
          </div>
        </div>

        <!-- 附件上传 -->
        <div class="form-section">
          <h2 class="form-section-title">附件上传</h2>
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <div class="flex flex-col items-center">
              <svg class="w-12 h-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              <p class="text-sm text-gray-500 mb-2">点击或拖拽文件到此处上传</p>
              <input type="file" multiple @change="handleFileUpload" class="hidden" ref="fileInput" />
              <button type="button" @click="$refs.fileInput.click()" class="btn btn-secondary">
                选择文件
              </button>
            </div>
            <!-- 已上传文件列表 -->
            <div v-if="purchaseForm.attachments && purchaseForm.attachments.length > 0" class="mt-4">
              <h4 class="text-sm font-medium text-gray-700 mb-2">已上传文件：</h4>
              <div class="space-y-2">
                <div v-for="(file, index) in purchaseForm.attachments" :key="index" class="flex items-center justify-between bg-gray-50 p-3 rounded-lg border">
                  <div class="flex items-center space-x-3">
                    <!-- 文件图标 -->
                    <div class="flex-shrink-0">
                      <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                    </div>
                    <!-- 文件信息 -->
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        {{ file.name || file.originalName || `附件${index + 1}` }}
                      </p>
                      <div class="flex items-center space-x-2 text-xs text-gray-500">
                        <span v-if="file.size">{{ formatFileSize(file.size) }}</span>
                        <span v-if="file.type">{{ file.type }}</span>
                        <span v-if="file.uploadTime">{{ formatUploadTime(file.uploadTime) }}</span>
                      </div>
                    </div>
                  </div>
                  <!-- 操作按钮 -->
                  <div class="flex items-center space-x-2">
                    <!-- 下载按钮 -->
                    <button
                      v-if="file.url"
                      type="button"
                      @click="downloadAttachment(file)"
                      class="text-blue-600 hover:text-blue-800 p-1"
                      title="下载文件"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                    </button>
                    <!-- 删除按钮 -->
                    <button
                      type="button"
                      @click="removeAttachment(index)"
                      class="text-red-600 hover:text-red-800 p-1"
                      title="删除文件"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <h2 class="form-section-title">备注信息</h2>
          <div>
            <label for="remarks" class="form-label">备注</label>
            <textarea
              id="remarks"
              v-model="purchaseForm.remarks"
              class="form-input"
              rows="3"
              placeholder="添加采购单备注信息"
            ></textarea>
          </div>
        </div>
        <!-- 底部操作按钮 -->
        <div class="flex justify-end space-x-4 mt-6">
          <button type="button" @click="resetForm" class="btn btn-secondary">重置</button>
          <button type="submit" @click="submitForm" class="btn btn-primary shadow-sm hover:shadow-md transition-all duration-200" :disabled="isLoading">
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
               {{ isEditMode ? '保存修改' : '保存采购单' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';

const router = useRouter();
const route = useRoute();

// 判断是编辑模式还是新增模式
const isEditMode = computed(() => !!route.params.id);
const purchaseId = computed(() => route.params.id);

// 数据状态
const suppliers = ref([]);
const products = ref([]);
const projects = ref([]);
const isLoading = ref(false);
const error = ref(null);
const successMessage = ref('');

// 获取供应商列表
async function fetchSuppliers() {
  try {
    const response = await axios.get('/api/suppliers');
    suppliers.value = response.data.results || [];
  } catch (err) {
    console.error('获取供应商列表失败:', err);
    error.value = '很抱歉，无法获取供应商列表信息。请检查您的网络连接，或稍后再试。如需帮助，请联系系统管理员。';
  }
}

// 获取产品列表
async function fetchProducts() {
  try {
    const response = await axios.get('/api/products');
    products.value = response.data.results || [];
  } catch (err) {
    console.error('获取产品列表失败:', err);
    error.value = '很抱歉，无法获取产品列表信息。请检查您的网络连接，或稍后再试。如需帮助，请联系系统管理员。';
  }
}

// 获取项目列表
async function fetchProjects() {
  try {
    const response = await axios.get('/api/projects');
    projects.value = response.data.results || [];
  } catch (err) {
    console.error('获取项目列表失败:', err);
    error.value = '很抱歉，无法获取项目列表信息。请检查您的网络连接，或稍后再试。如需帮助，请联系系统管理员。';
  }
}

// 表单数据 - 与后端模型字段保持一致
const purchaseForm = ref({
  // 后端模型字段
  purchaseNumber: '系统生成', // 对应前端的 number
  purchaseDate: new Date().toISOString().substr(0, 10), // 对应前端的 date
  supplierId: '',
  status: 'draft',
  totalAmount: 0, // 总金额，由前端计算
  currency: 'CNY', // 默认货币
  items: [createEmptyItem()],
  notes: '', // 对应前端的 remarks

  // 可选字段
  projectId: null,
  expectedDeliveryDate: null,
  deliveryAddress: '',
  recipient: '', // 收货人
  attachments: [], // 附件列表

  // 前端特有字段（不在后端模型中）
  number: '系统生成', // 临时存储，提交时会转换为 purchaseNumber
  date: new Date().toISOString().substr(0, 10), // 临时存储，提交时会转换为 purchaseDate
  remarks: '', // 临时存储，提交时会转换为 notes
});

// 创建空的采购项目 - 与后端模型字段保持一致
function createEmptyItem() {
  return {
    // 后端模型字段
    name: '',
    quantity: 1,
    unit: '个',
    unitPrice: 0, // 对应前端的 price
    totalPrice: 0, // 对应前端的 amount
    description: '', // 对应前端的 specification

    // 税务相关字段
    priceExcludingTax: 0, // 不含税单价
    taxRate: 0.13, // 默认税率13%
    priceIncludingTax: 0, // 含税单价
    taxAmount: 0, // 税额
    totalExcludingTax: 0, // 不含税总额
    amountIncludingTax: 0, // 含税金额

    // 前端特有字段（不在后端模型中）
    productId: '', // 用于前端选择产品
    specification: '', // 临时存储，提交时会转换为 description
    price: 0, // 临时存储，提交时会转换为 unitPrice
    amount: 0 // 临时存储，提交时会转换为 totalPrice
  };
}

// 添加采购项目
function addItem() {
  purchaseForm.value.items.push(createEmptyItem());
}

// 移除采购项目
function removeItem(index) {
  if (purchaseForm.value.items.length > 1) {
    purchaseForm.value.items.splice(index, 1);
  }
}

// 计算单项金额
function calculateItemAmount(index) {
  const item = purchaseForm.value.items[index];

  // 确保数值类型
  const quantity = Number(item.quantity) || 0;
  const priceExcludingTax = Number(item.priceExcludingTax) || 0;
  const taxRate = Number(item.taxRate) || 0;

  // 计算税务相关金额
  item.priceIncludingTax = priceExcludingTax * (1 + taxRate); // 含税单价
  item.taxAmount = priceExcludingTax * taxRate; // 单位税额
  item.totalExcludingTax = quantity * priceExcludingTax; // 不含税总额
  item.amountIncludingTax = quantity * item.priceIncludingTax; // 含税金额

  // 更新前端兼容字段
  item.price = priceExcludingTax;
  item.amount = item.totalExcludingTax;

  // 同步更新后端字段
  item.unitPrice = priceExcludingTax;
  item.totalPrice = item.amountIncludingTax;

  // 更新采购单总金额
  purchaseForm.value.totalAmount = totalAmountIncludingTax.value;
}

// 计算总金额
const totalAmount = computed(() => {
  return purchaseForm.value.items.reduce((sum, item) => sum + (item.amount || 0), 0);
});

// 计算不含税总额
const totalAmountExcludingTax = computed(() => {
  return purchaseForm.value.items.reduce((sum, item) => sum + (item.totalExcludingTax || 0), 0);
});

// 计算税额合计
const totalTaxAmount = computed(() => {
  return purchaseForm.value.items.reduce((sum, item) => sum + (item.taxAmount * item.quantity || 0), 0);
});

// 计算含税总额
const totalAmountIncludingTax = computed(() => {
  return purchaseForm.value.items.reduce((sum, item) => sum + (item.amountIncludingTax || 0), 0);
});

// 货币格式化
function formatCurrency(value) {
  return value.toFixed(2);
}

// 处理文件上传
function handleFileUpload(event) {
  const files = Array.from(event.target.files);
  purchaseForm.value.attachments = [...purchaseForm.value.attachments, ...files];
  event.target.value = null;
}

// 移除附件
function removeAttachment(index) {
  purchaseForm.value.attachments.splice(index, 1);
}

// 下载附件
function downloadAttachment(file) {
  if (file.url) {
    // 如果有URL，直接下载
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name || file.originalName || 'attachment';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } else {
    console.warn('附件没有下载链接');
  }
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化上传时间
function formatUploadTime(timeString) {
  if (!timeString) return '';
  const date = new Date(timeString);
  return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

// 重置表单
function resetForm() {
  if (isEditMode.value) {
    // 如果是编辑模式，重新获取原始数据
    fetchPurchaseDetail();
  } else {
    // 如果是新建模式，清空表单
    purchaseForm.value = {
      // 后端模型字段
      purchaseNumber: '系统生成',
      purchaseDate: new Date().toISOString().substr(0, 10),
      supplierId: '',
      status: 'draft',
      totalAmount: 0,
      currency: 'CNY',
      items: [createEmptyItem()],
      notes: '',

      // 可选字段
      projectId: null,
      expectedDeliveryDate: null,
      deliveryAddress: '',
      recipient: '',
      attachments: [],

      // 前端特有字段
      number: '系统生成',
      date: new Date().toISOString().substr(0, 10),
      remarks: ''
    };
  }
}

// 提交表单
async function submitForm() {
  try {
    isLoading.value = true;
    error.value = null;

    // 验证表单
    if (!purchaseForm.value.supplierId) {
      error.value = '请选择一个供应商。采购单需要关联到特定供应商才能继续。';
      return;
    }

    if (purchaseForm.value.items.length === 0) {
      error.value = '请添加至少一个采购项目。采购单需要包含具体的采购物品信息才能提交。';
      return;
    }

    // 验证每个采购项目
    for (let i = 0; i < purchaseForm.value.items.length; i++) {
      const item = purchaseForm.value.items[i];
      if (!item.productId) {
        error.value = `请为第 ${i+1} 个采购项目选择具体产品。每个采购项目都需要明确的产品信息。`;
        return;
      }
      if (item.quantity <= 0) {
        error.value = `第 ${i+1} 个采购项目的数量必须大于0。请输入有效的采购数量。`;
        return;
      }
      if (item.price < 0) {
        error.value = `第 ${i+1} 个采购项目的价格不能为负数。请输入有效的单价金额。`;
        return;
      }
    }

    // 准备提交数据，直接使用后端模型字段
    // 处理采购项目，确保每个项目都有必要的字段
    const formattedItems = purchaseForm.value.items.map(item => {
      // 查找产品信息
      const product = products.value.find(p => p.id === item.productId) || {};

      // 更新项目的后端字段
      item.name = product.name || '未知产品';
      item.unit = product.unit || '个';
      item.description = item.specification || '';

      // 返回符合后端模型的项目数据
      return {
        name: item.name,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        description: item.description
      };
    });

    // 构建提交数据，使用后端模型字段
    const purchaseData = {
      // 必要字段
      purchaseNumber: purchaseForm.value.purchaseNumber !== '系统生成' ?
                      purchaseForm.value.purchaseNumber :
                      `PO-${Date.now()}`,
      supplierId: purchaseForm.value.supplierId,
      status: purchaseForm.value.status,
      totalAmount: purchaseForm.value.totalAmount,
      currency: purchaseForm.value.currency,
      purchaseDate: purchaseForm.value.purchaseDate,
      items: formattedItems,
      notes: purchaseForm.value.notes,

      // 可选字段
      projectId: purchaseForm.value.projectId || null,
      expectedDeliveryDate: purchaseForm.value.expectedDeliveryDate || null,
      deliveryAddress: purchaseForm.value.deliveryAddress || '',
      recipient: purchaseForm.value.recipient || ''
    };

    // 提交表单数据
    if (isEditMode.value) {
      // 编辑模式：调用更新API
      await axios.patch(`/api/purchases/${purchaseId.value}`, purchaseData);
      console.log('采购单更新成功!');
      successMessage.value = '采购单更新成功!';
    } else {
      // 新增模式：调用创建API
      await axios.post('/api/purchases', purchaseData);
      console.log('采购单创建成功!');
      successMessage.value = '采购单创建成功!';
    }

    // 延迟一小时后跳转到采购单列表页面
    setTimeout(() => {
      router.push('/purchases');
    }, 1500);
  } catch (err) {
    console.error('提交采购单失败:', err);
    error.value = err.response?.data?.message || '很抱歉，提交采购单时遇到了问题。请检查您的网络连接和输入信息是否正确，然后再次尝试提交。如果问题持续存在，请联系系统管理员获取帮助。';
  } finally {
    isLoading.value = false;
  }
}

// 获取采购单详情
async function fetchPurchaseDetail() {
  isLoading.value = true;
  error.value = null;

  try {
    console.log(`正在获取采购单详情，ID: ${purchaseId.value}`);
    const response = await axios.get(`/api/purchases/${purchaseId.value}`);
    const purchaseData = response.data;

    console.log('后端返回的采购单数据:', JSON.stringify(purchaseData, null, 2));
    console.log('当前可用的产品列表:', products.value.length, '个产品');

    // 填充表单数据
    purchaseForm.value = {
      // 后端模型字段
      purchaseNumber: purchaseData.purchaseNumber || '系统生成',
      purchaseDate: purchaseData.purchaseDate || new Date().toISOString().substr(0, 10),
      supplierId: purchaseData.supplierId || '',
      status: purchaseData.status || 'draft',
      totalAmount: purchaseData.totalAmount || 0,
      currency: purchaseData.currency || 'CNY',
      items: [],
      notes: purchaseData.notes || '',

      // 可选字段
      projectId: purchaseData.projectId || null,
      expectedDeliveryDate: purchaseData.expectedDeliveryDate || null,
      deliveryAddress: purchaseData.deliveryAddress || '',
      recipient: purchaseData.recipient || '',
      attachments: purchaseData.attachments || [],

      // 前端特有字段
      number: purchaseData.purchaseNumber || '系统生成',
      date: purchaseData.purchaseDate || new Date().toISOString().substr(0, 10),
      remarks: purchaseData.notes || ''
    };

    // 处理附件数据
    console.log('开始处理附件数据...');
    if (purchaseData.attachments && purchaseData.attachments.length > 0) {
      console.log(`发现 ${purchaseData.attachments.length} 个附件`);
      purchaseForm.value.attachments = purchaseData.attachments.map((attachment, index) => {
        console.log(`处理第 ${index + 1} 个附件:`, JSON.stringify(attachment, null, 2));
        return {
          name: attachment.name || attachment.originalName || `附件${index + 1}`,
          originalName: attachment.originalName || attachment.name,
          url: attachment.url || attachment.path,
          size: attachment.size || 0,
          type: attachment.type || attachment.mimeType || '',
          uploadTime: attachment.uploadTime || attachment.createdAt || new Date().toISOString()
        };
      });
      console.log(`成功处理 ${purchaseForm.value.attachments.length} 个附件`);
    } else {
      console.log('没有附件数据');
      purchaseForm.value.attachments = [];
    }

    // 处理采购项目
    console.log('开始处理采购项目...');
    if (purchaseData.items && purchaseData.items.length > 0) {
      console.log(`发现 ${purchaseData.items.length} 个采购项目`);

      purchaseForm.value.items = purchaseData.items.map((item, index) => {
        console.log(`处理第 ${index + 1} 个采购项目:`, JSON.stringify(item, null, 2));

        // 尝试根据产品名称查找产品ID（用于编辑模式）
        let productId = item.productId || '';
        if (!productId && item.name && products.value.length > 0) {
          const matchedProduct = products.value.find(p => p.name === item.name);
          productId = matchedProduct ? matchedProduct.id : '';
          console.log(`根据产品名称 "${item.name}" 匹配到产品ID: ${productId}`);
        } else if (productId) {
          console.log(`使用现有产品ID: ${productId}`);
        } else {
          console.log(`产品ID为空，产品名称: ${item.name}`);
        }

        const mappedItem = {
          // 后端模型字段
          name: item.name || '',
          quantity: item.quantity || 1,
          unit: item.unit || '个',
          unitPrice: item.unitPrice || 0,
          totalPrice: item.totalPrice || 0,
          description: item.description || '',

          // 前端特有字段（兼容新旧版本）
          productId: productId,
          specification: item.description || item.specification || '',
          price: item.unitPrice || item.price || 0,
          amount: item.totalPrice || item.amount || 0,

          // 税率相关字段
          priceExcludingTax: item.priceExcludingTax || item.unitPrice || item.price || 0,
          taxRate: item.taxRate !== undefined ? item.taxRate : 0.13,
          priceIncludingTax: item.priceIncludingTax || 0,
          taxAmount: item.taxAmount || 0,
          amountIncludingTax: item.amountIncludingTax || item.totalPrice || item.amount || 0
        };

        console.log(`映射后的采购项目:`, JSON.stringify(mappedItem, null, 2));
        return mappedItem;
      });

      console.log(`成功映射 ${purchaseForm.value.items.length} 个采购项目`);
      
      // 重新计算每个采购项目的金额
      purchaseForm.value.items.forEach((item, index) => {
        calculateItemAmount(index);
      });
    } else {
      console.log('没有采购项目数据，创建空项目');
      // 如果没有items数据，创建一个空项目
      purchaseForm.value.items = [createEmptyItem()];
    }

    console.log('最终的表单数据:', JSON.stringify(purchaseForm.value, null, 2));
  } catch (err) {
    console.error('获取采购单详情失败:', err);
    error.value = err.response?.data?.message || '很抱歉，无法获取采购单详细信息。可能是网络连接问题或该采购单不存在。请检查网络连接后再试，或返回列表页查看其他采购单。';
  } finally {
    isLoading.value = false;
  }
}

// 处理产品选择变化
function handleProductChange(index) {
  const item = purchaseForm.value.items[index];
  const selectedProduct = products.value.find(p => p.id === item.productId);

  if (selectedProduct) {
    // 设置产品不含税单价
    item.priceExcludingTax = selectedProduct.price || 0;
    // 设置产品单位
    item.unit = selectedProduct.unit || '个';
    // 设置默认税率（如果产品有税率信息）
    if (selectedProduct.taxRate !== undefined) {
      item.taxRate = selectedProduct.taxRate;
    } else {
      // 默认税率为13%
      item.taxRate = 0.13;
    }
    // 兼容旧字段
    item.price = item.priceExcludingTax;
    // 重新计算金额
    calculateItemAmount(index);
  }
}

onMounted(async () => {
  console.log(isEditMode.value ? '采购单编辑页面已加载' : '采购单创建页面已加载');
  isLoading.value = true;

  try {
    // 先加载基础数据（供应商、产品、项目）
    console.log('开始加载基础数据...');
    await Promise.all([
      fetchSuppliers(),
      fetchProducts(),
      fetchProjects()
    ]);
    console.log('基础数据加载完成');

    // 如果是编辑模式，在基础数据加载完成后再获取采购单详情
    if (isEditMode.value) {
      console.log('开始加载采购单详情...');
      await fetchPurchaseDetail();
      console.log('采购单详情加载完成');
    }
  } catch (err) {
    console.error('加载数据失败:', err);
    error.value = '加载页面数据失败，请刷新页面重试';
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.page-header {
  @apply flex justify-between items-center mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}

.card {
  @apply bg-white rounded-xl p-6 mb-6;
  animation: fadeSlideIn 0.6s ease-out;
}

.form-section {
  @apply border-b border-gray-200 pb-6 mb-6 last:border-0 last:pb-0 last:mb-0;
}

.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50;
}

.btn {
  @apply py-2 px-4 rounded-lg focus:outline-none focus:ring transition-all duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-200;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-200;
}

.back-button {
  @apply text-gray-600 hover:text-gray-900 focus:outline-none;
}

.form-required {
  @apply text-red-500;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>