const { sequelize } = require('../config/database');

const db = {};

// Import models
const { User } = require('./user.model');
const { Category } = require('./category.model');
const { Tag } = require('./tag.model');
const { Supplier, setupAssociations: setupSupplierAssociations } = require('./supplier.model');
const { Project } = require('./project.model');
const { Token } = require('./token.model');
const { Permission } = require('./permission.model');
const { Role } = require('./role.model');
const { Document, setupAssociations: setupDocumentAssociations } = require('./document.model');
const { Comment } = require('./comment.model');
const { Attachment, setupAssociations: setupAttachmentAssociations } = require('./attachment.model');
const { ProjectTask } = require('./projectTask.model');
const { ProjectProgress } = require('./projectProgress.model');
const { Product, setupAssociations: setupProductAssociations } = require('./product.model');
const { ProductTransaction, setupAssociations: setupProductTransactionAssociations } = require('./productTransaction.model');
const { Finance, setupAssociations: setupFinanceAssociations } = require('./finance.model');
const { Customer } = require('./customer.model');
const { Client, setupAssociations: setupClientAssociations } = require('./client.model');
const { Contract, setupAssociations: setupContractAssociations } = require('./contract.model');
const { Attendance, setupAssociations: setupAttendanceAssociations } = require('./attendance.model');
const { ProjectFollowUp } = require('./projectFollowUp.model');
const { Employee } = require('./employee.model');
const { Reimbursement, setupAssociations: setupReimbursementAssociations } = require('./reimbursement.model');
const { WorkHour } = require('./workhour.model');
const { Task, setupAssociations: setupTaskAssociations } = require('./task.model');
const { Purchase, setupAssociations: setupPurchaseAssociations } = require('./purchase.model');
const { Department } = require('./department.model');

// Import new models
const { Subcontract, setupAssociations: setupSubcontractAssociations } = require('./subcontract.model');
const { UserMessage, setupAssociations: setupUserMessageAssociations } = require('./userMessage.model');

// Import junction tables and set up associations
const { DocumentTag } = require('./documentTag.model');

// Add models to db object
db.User = User;
db.Token = Token;
db.Supplier = Supplier;
db.Tag = Tag;
db.Category = Category;
db.Project = Project;
db.Permission = Permission;
db.Role = Role;
db.Document = Document;
db.Comment = Comment;
db.Attachment = Attachment;
db.ProjectTask = ProjectTask;
db.ProjectProgress = ProjectProgress;
db.Product = Product;
db.ProductTransaction = ProductTransaction;
db.Finance = Finance;
db.Customer = Customer;
db.Client = Client;
db.Contract = Contract;
db.Attendance = Attendance;
db.ProjectFollowUp = ProjectFollowUp;
db.Employee = Employee;
db.Reimbursement = Reimbursement;
db.WorkHour = WorkHour;
db.DocumentTag = DocumentTag;
db.Subcontract = Subcontract;
db.Task = Task;
db.Purchase = Purchase;
db.Department = Department;
db.UserMessage = UserMessage;

// Set up ProjectFollowUp associations
ProjectFollowUp.belongsTo(Project, {
  foreignKey: 'projectId',
  as: 'project'
});

ProjectFollowUp.belongsTo(Employee, {
  foreignKey: 'createdBy',
  as: 'creator'
});

// Set up associations
setupSupplierAssociations(db);
setupDocumentAssociations(db);
setupProductAssociations(db);
setupProductTransactionAssociations(db);
setupFinanceAssociations(db);
setupClientAssociations(db);
setupContractAssociations(db);
setupAttendanceAssociations(db);
setupSubcontractAssociations(db);
setupTaskAssociations(db);
setupPurchaseAssociations(db);
setupAttachmentAssociations(db);
setupReimbursementAssociations(db);
setupUserMessageAssociations(db);

// Run associations for Reimbursement model
if (Reimbursement.associate) {
  Reimbursement.associate(db);
}

// Run associations for Department model
if (Department.associate) {
  Department.associate(db);
}

// Run associations for User model
if (User.associate) {
  User.associate(db);
}

db.sequelize = sequelize;

module.exports = db;